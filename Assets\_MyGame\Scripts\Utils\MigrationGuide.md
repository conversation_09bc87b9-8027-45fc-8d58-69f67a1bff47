# 从 ObscuredTypes 迁移到 SimpleObscured 指南

## 迁移概述

本指南帮助你将现有代码从 `CodeStage.AntiCheat.ObscuredTypes` 迁移到我们的简化版本 `MyGame.Utils.SimpleObscured*`。

## 迁移步骤

### 1. 替换命名空间引用

**原代码:**
```csharp
using CodeStage.AntiCheat.ObscuredTypes;
```

**新代码:**
```csharp
using MyGame.Utils;
```

### 2. 替换类型名称

**原代码:**
```csharp
public ObscuredInt attack { get; set; }
public ObscuredLong curHp { get; set; }
public ObscuredLong maxHp { get; set; }
```

**新代码:**
```csharp
public SimpleObscuredInt attack { get; set; }
public SimpleObscuredLong curHp { get; set; }
public SimpleObscuredLong maxHp { get; set; }
```

### 3. 批量替换示例

你可以使用IDE的查找替换功能进行批量替换：

1. **替换 ObscuredInt:**
   - 查找: `ObscuredInt`
   - 替换: `SimpleObscuredInt`

2. **替换 ObscuredLong:**
   - 查找: `ObscuredLong`
   - 替换: `SimpleObscuredLong`

3. **替换命名空间:**
   - 查找: `using CodeStage.AntiCheat.ObscuredTypes;`
   - 替换: `using MyGame.Utils;`

### 4. 针对 BattleUnit.cs 的具体迁移

**原代码 (BattleUnit.cs):**
```csharp
using CodeStage.AntiCheat.ObscuredTypes;

public class BattleUnit : BaseUnit, IBattleObject
{
    public ObscuredInt attack { get; set; }
    public ObscuredLong curHp { get; set; }
    public ObscuredLong maxHp { get; set; }
    public ObscuredLong curShieldHp { get; set; }
    public ObscuredLong maxShieldHp { get; set; }
    
    // ... 其他代码保持不变
}
```

**新代码 (BattleUnit.cs):**
```csharp
using MyGame.Utils;

public class BattleUnit : BaseUnit, IBattleObject
{
    public SimpleObscuredInt attack { get; set; }
    public SimpleObscuredLong curHp { get; set; }
    public SimpleObscuredLong maxHp { get; set; }
    public SimpleObscuredLong curShieldHp { get; set; }
    public SimpleObscuredLong maxShieldHp { get; set; }
    
    // ... 其他代码保持不变
}
```

## 兼容性说明

### 完全兼容的功能
- 基本赋值和读取操作
- 算术运算符 (+, -, *, /, %, ++, --)
- 比较运算符 (==, !=, >, <, >=, <=)
- 隐式类型转换
- Unity 序列化
- ToString() 方法

### 移除的功能
以下功能在简化版本中不可用：

1. **ObscuredCheatingDetector 集成**
   - 原版会自动检测作弊行为
   - 简化版本移除了这个功能

2. **Hash 验证**
   - 原版使用 hash 验证数据完整性
   - 简化版本移除了这个功能

3. **Honeypot 机制**
   - 原版支持假值诱饵
   - 简化版本移除了这个功能

4. **高级密钥管理**
   - 原版使用 ThreadSafeRandom
   - 简化版本使用 UnityEngine.Random

### 新增的便利功能
- `GetDecrypted()` 方法：显式获取解密值
- `RandomizeCryptoKey()` 方法：重新随机化密钥

## 迁移后的验证

### 1. 编译检查
确保所有文件都能正常编译，没有类型错误。

### 2. 功能测试
使用提供的 `SimpleObscuredTest.cs` 脚本验证功能：

```csharp
// 在场景中添加 SimpleObscuredTest 组件
// 运行游戏，查看控制台输出
// 按 T 键运行测试，按 P 键运行性能测试
```

### 3. 数值验证
确保迁移后的数值计算结果与原版一致：

```csharp
// 测试基本运算
SimpleObscuredInt a = 10;
SimpleObscuredInt b = 5;
Debug.Log($"10 + 5 = {a + b}"); // 应该输出 15

// 测试与普通数值的兼容性
int normalInt = a; // 应该等于 10
SimpleObscuredInt fromNormal = normalInt; // 应该等于 10
```

## 性能考虑

### 性能对比
- 简化版本比原版稍快（移除了额外的验证）
- 仍比普通数值慢 2-3 倍（加密解密开销）
- 适合用于重要数值，不适合频繁计算

### 优化建议
```csharp
// 不推荐：频繁访问加密值
for (int i = 0; i < 1000; i++)
{
    if (player.curHp > 0) // 每次都要解密
    {
        // 处理逻辑
    }
}

// 推荐：缓存解密值
long currentHp = player.curHp; // 只解密一次
for (int i = 0; i < 1000; i++)
{
    if (currentHp > 0)
    {
        // 处理逻辑
    }
}
```

## 常见问题

### Q: 迁移后数值显示异常？
A: 检查是否正确替换了所有类型名称，确保没有遗漏的 `ObscuredInt/Long`。

### Q: 序列化数据丢失？
A: Unity 会自动处理序列化迁移，但建议备份项目后再进行迁移。

### Q: 性能下降明显？
A: 这是正常的，加密数值本身就有性能开销。考虑只在关键数值上使用。

### Q: 需要重新配置什么吗？
A: 不需要，简化版本设计为即插即用，无需额外配置。

## 迁移检查清单

- [ ] 替换所有 `using CodeStage.AntiCheat.ObscuredTypes;`
- [ ] 替换所有 `ObscuredInt` 为 `SimpleObscuredInt`
- [ ] 替换所有 `ObscuredLong` 为 `SimpleObscuredLong`
- [ ] 编译检查无错误
- [ ] 运行测试脚本验证功能
- [ ] 测试游戏核心功能正常
- [ ] 检查数值计算结果正确
- [ ] 验证序列化数据完整

## 回滚方案

如果迁移后出现问题，可以快速回滚：

1. 恢复原始的 `using CodeStage.AntiCheat.ObscuredTypes;`
2. 将 `SimpleObscuredInt/Long` 改回 `ObscuredInt/Long`
3. 重新编译项目

建议在迁移前创建代码分支或备份，以便快速回滚。
