using System;
using System.Runtime.InteropServices;
using UnityEngine;


/// <summary>
/// 简化版本的ObscuredFloat，用于热更代码中的浮点数值保护
/// </summary>
[Serializable]
public struct SimpleObscuredFloat : IEquatable<SimpleObscuredFloat>, IEquatable<float>, IComparable<SimpleObscuredFloat>, IComparable<float>, IComparable
{
    [SerializeField] private int hiddenValue;
    [SerializeField] private int currentCryptoKey;

    private SimpleObscuredFloat(float value)
    {
        currentCryptoKey = GenerateKey();
        hiddenValue = Encrypt(value, currentCryptoKey);
    }

    /// <summary>
    /// 加密浮点数值
    /// </summary>
    public static int Encrypt(float value, int key)
    {
        var union = new FloatIntUnion { floatValue = value };
        return union.intValue ^ key;
    }

    /// <summary>
    /// 解密浮点数值
    /// </summary>
    public static float Decrypt(int value, int key)
    {
        var union = new FloatIntUnion { intValue = value ^ key };
        return union.floatValue;
    }

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    public static int GenerateKey()
    {
        // 使用System.Random避免Unity的限制
        var seed = System.DateTime.Now.Millisecond;
        var random = new System.Random(seed);
        return random.Next(1000000000, int.MaxValue);
    }

    /// <summary>
    /// 获取解密后的值
    /// </summary>
    public float GetDecrypted()
    {
        return InternalDecrypt();
    }

    /// <summary>
    /// 随机化密钥
    /// </summary>
    public void RandomizeCryptoKey()
    {
        var decrypted = InternalDecrypt();
        currentCryptoKey = GenerateKey();
        hiddenValue = Encrypt(decrypted, currentCryptoKey);
    }

    private float InternalDecrypt()
    {
        if (IsDefault()) 
            return 0f;
        
        return Decrypt(hiddenValue, currentCryptoKey);
    }

    private bool IsDefault()
    {
        return hiddenValue == default && currentCryptoKey == default;
    }

    /// <summary>
    /// 比较两个浮点数是否相等（考虑浮点精度）
    /// </summary>
    private static bool Compare(float a, float b)
    {
        return Mathf.Approximately(a, b);
    }

    // 隐式转换操作符
    public static implicit operator SimpleObscuredFloat(float value)
    {
        return new SimpleObscuredFloat(value);
    }

    public static implicit operator float(SimpleObscuredFloat value)
    {
        return value.InternalDecrypt();
    }

    // 算术操作符
    public static SimpleObscuredFloat operator ++(SimpleObscuredFloat input)
    {
        return input.InternalDecrypt() + 1f;
    }

    public static SimpleObscuredFloat operator --(SimpleObscuredFloat input)
    {
        return input.InternalDecrypt() - 1f;
    }

    public static SimpleObscuredFloat operator +(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        return a.InternalDecrypt() + b.InternalDecrypt();
    }

    public static SimpleObscuredFloat operator -(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        return a.InternalDecrypt() - b.InternalDecrypt();
    }

    public static SimpleObscuredFloat operator *(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        return a.InternalDecrypt() * b.InternalDecrypt();
    }

    public static SimpleObscuredFloat operator /(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        return a.InternalDecrypt() / b.InternalDecrypt();
    }

    public static SimpleObscuredFloat operator %(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        return a.InternalDecrypt() % b.InternalDecrypt();
    }

    // 比较操作符
    public static bool operator ==(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        return Compare(a.InternalDecrypt(), b.InternalDecrypt());
    }

    public static bool operator !=(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        return !Compare(a.InternalDecrypt(), b.InternalDecrypt());
    }

    public static bool operator >(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        return a.InternalDecrypt() > b.InternalDecrypt();
    }

    public static bool operator <(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        return a.InternalDecrypt() < b.InternalDecrypt();
    }

    public static bool operator >=(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        var aVal = a.InternalDecrypt();
        var bVal = b.InternalDecrypt();
        return aVal > bVal || Compare(aVal, bVal);
    }

    public static bool operator <=(SimpleObscuredFloat a, SimpleObscuredFloat b)
    {
        var aVal = a.InternalDecrypt();
        var bVal = b.InternalDecrypt();
        return aVal < bVal || Compare(aVal, bVal);
    }

    // 接口实现
    public bool Equals(SimpleObscuredFloat other)
    {
        return Compare(InternalDecrypt(), other.InternalDecrypt());
    }

    public bool Equals(float other)
    {
        return Compare(InternalDecrypt(), other);
    }

    public override bool Equals(object obj)
    {
        if (obj is SimpleObscuredFloat obscured)
            return Equals(obscured);
        if (obj is float floatValue)
            return Equals(floatValue);
        return false;
    }

    public override int GetHashCode()
    {
        return InternalDecrypt().GetHashCode();
    }

    public int CompareTo(SimpleObscuredFloat other)
    {
        return InternalDecrypt().CompareTo(other.InternalDecrypt());
    }

    public int CompareTo(float other)
    {
        return InternalDecrypt().CompareTo(other);
    }

    public int CompareTo(object obj)
    {
        if (obj is SimpleObscuredFloat obscured)
            return CompareTo(obscured);
        if (obj is float floatValue)
            return CompareTo(floatValue);
        throw new ArgumentException("Object must be of type SimpleObscuredFloat or float");
    }

    public override string ToString()
    {
        return InternalDecrypt().ToString();
    }

    public string ToString(string format)
    {
        return InternalDecrypt().ToString(format);
    }

    public string ToString(IFormatProvider provider)
    {
        return InternalDecrypt().ToString(provider);
    }

    public string ToString(string format, IFormatProvider provider)
    {
        return InternalDecrypt().ToString(format, provider);
    }

    /// <summary>
    /// 用于float和int之间的位级转换的联合体
    /// </summary>
    [StructLayout(LayoutKind.Explicit)]
    private struct FloatIntUnion
    {
        [FieldOffset(0)] public float floatValue;
        [FieldOffset(0)] public int intValue;
    }
}

