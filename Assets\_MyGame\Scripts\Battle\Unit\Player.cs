using Cysharp.Threading.Tasks;
using DG.Tweening;
using FairyGUI;
using Proto.LogicData;
using Proto.Types;
using Spine.Unity;
using System;
using System.Collections.Generic;
using UnityEngine;

/**
 * 玩家对象
 */
public class Player : BattleUnit, IBattleObject
{
    private SimpleObscuredInt _protectedAttack;
    private SimpleObscuredLong _protectedCurHp;
    private SimpleObscuredLong _protectedMaxHp;
    private SimpleObscuredLong _protectedCurShieldHp;
    private SimpleObscuredLong _protectedMaxShieldHp;

    public new int attack
    {
        get => _protectedAttack;
        set => _protectedAttack = value;
    }

    public new long curHp
    {
        get => _protectedCurHp;
        set => _protectedCurHp = value;
    }

    public new long maxHp
    {
        get => _protectedMaxHp;
        set => _protectedMaxHp = value;
    }

    public new long curShieldHp
    {
        get => _protectedCurShieldHp;
        set => _protectedCurShieldHp = value;
    }

    public new long maxShieldHp
    {
        get => _protectedMaxShieldHp;
        set => _protectedMaxShieldHp = value;
    }

    public bool isDebug = false;

    public Transform weaponNode;
    public Transform[] followNodes;
    public ProgressBar hpBar;

    public PlacePos[] heroNodes;

    public PlacePos[] heroTowerNodes;

    public float maxFarAway = 10;
    public float targetOrthoSize = 9;
    public Transform changeSeatTip;

    /// <summary>
    /// 减伤百分比
    /// </summary>
    protected float _reduceHarmPercent = 0f;

    /// <summary>
    /// 减伤百分比最大值
    /// </summary>
    protected float _reduceHarmPercentMax = 0f;

    protected int _reduceHarmParam = 0;

    [SerializeField] LayerMask LayerMaskPickup;
    [SerializeField] LayerMask LayerHeroPos;

    [NonSerialized]
    public Action<Player> OnDieAction;
    [NonSerialized]
    public Action<InfoDropItem> OnCollectItem;

    [NonSerialized]
    public int curExp = 0;
    [NonSerialized]
    public int maxExp = 0;
    [NonSerialized]
    public int level = 0;
    [NonSerialized]
    public int gold = 0;
    [NonSerialized]
    public int chest = 0;

    private bool _unHurt = false;

    private SkeletonAnimation _aniWallBoom;     //城墙倒塌
    private  SkeletonAnimation _aniRepair;      //修复动画

    protected PlacePos _dragStartPos;        //拖拽英雄的起始位置
    protected Transform _attackRange;      //攻击范围

    public int killCount { get; internal set; }
    public int killBossCount { get; internal set; }
   // public int selectAllAddSkillCount { get; internal set; }
   // public int selectAllAtkSkillCount { get; internal set; }


    protected Dictionary<int, Hero> _heroDict = new Dictionary<int, Hero>();

    private GameObject debugGo;

    protected Wall _wall;               //城墙
    protected BattlePet _pet;           //宠物
    protected BattleArtifact _artifact; //宝具

    protected List<int> _battleProgressSkills;     //战斗进度的技能

    //private MaterialPropertyBlock materialBlock;
    //private SpriteRenderer spriteRenderer;
    private List<int> uniqueSkills = new List<int>();//所有必杀技
    private Dictionary<int, int> equipedUniqueSkillDic = new Dictionary<int, int>();//已装备的必杀技

    [SerializeField] RectArea bulletRandomArea;
    override protected void OnAwake()
    {
        base.OnAwake();
        hpBar = this.GetComponentInChildren<ProgressBar>();
        Debug.Assert(hpBar != null);
        tag = TagEnum.Player.ToString();

        _attackRange = this.transform.Find("AttackRange");
        Debug.Assert(_attackRange != null);

        _pet = this.GetComponentInChildren<BattlePet>();
        _artifact = this.GetComponentInChildren<BattleArtifact>();

        this.killCount = 0;
        this.killBossCount = 0;
        _battleProgressSkills = new List<int>();

        this.curHp = 0;

        //初始化站位索引
        for (int i = 0; i < heroNodes.Length; ++i) {
            heroNodes[i].index = i;
        }
        for (int i = 0; i < heroTowerNodes.Length; ++i) {
            heroTowerNodes[i].index = i;
        }
    }

    override protected void OnStart()
    {
        faceDir = 1;
        isDie = false;
        level = 0;
        var wallRepairObj = this.transform.Find("WallRepair");
        if (wallRepairObj != null) {
            _aniRepair = wallRepairObj.GetComponentInChildren<SkeletonAnimation>();
            _aniRepair.gameObject.SetActive(false);
        }


        var wallBoomObj = this.transform.Find("WallBoom");
        if (wallBoomObj != null) {
            _aniWallBoom = wallBoomObj.GetComponentInChildren<SkeletonAnimation>();
            _aniWallBoom.gameObject.SetActive(false);
        }
        UpdateBaseAttr();
        //LevelUp();
    }

    public void InitReduceHarmPercent(int reduceHarmParam, float maxPercent)
    {
        _reduceHarmParam = reduceHarmParam;
        _reduceHarmPercentMax = maxPercent;

        ComputeReduceHarmPercent();
    }

    protected void ComputeReduceHarmPercent()
    {
        _reduceHarmPercent = 0f;
        int totalReduceHarm = GetReduceHarm();
        if (_reduceHarmParam + totalReduceHarm > 0) {
            _reduceHarmPercent = (float)totalReduceHarm / (float)(_reduceHarmParam + totalReduceHarm);
        }
        if (_reduceHarmPercent > _reduceHarmPercentMax) {
            _reduceHarmPercent = _reduceHarmPercentMax;
        }
    }

    public int GetReduceHarm()
    {
        int totalReduceHarm = GetAttrValue(BattleAttrType.ReduceHarm);
        float addPercent = GetReduceHarmAddPercent();
        totalReduceHarm = (int)(totalReduceHarm * (1f + addPercent));
        return totalReduceHarm;
    }

    public float GetReduceHarmAddPercent()
    {
        float addPercent = GetAttrPercent(BattleAttrType.ReduceHarmAddPercent);
        return addPercent;
    }

    /// <summary>
    /// 设置城墙
    /// </summary>
    /// <param name="wall"></param>
    public void SetWall(Wall wall)
    {
        this._wall = wall;
    }

    public BattlePet GetPet()
    {
        return this._pet;
    }

    public BattleArtifact GetBattleArtifact()
    {
        return this._artifact;
    }

    override public long GetHp()
    {
        return this.curHp;
    }

    override public long GetMaxHp()
    {
        float addPercent = GetHpMaxAddPercent();
        long curMaxHp = (long)(this.maxHp * (1f + addPercent));
        return curMaxHp;
    }

    protected float GetHpMaxAddPercent()
    {
        return this.GetAttrPercent(BattleAttrType.HpMaxAddPercent);
    }

    public int GetGold()
    {
        return this.gold;
    }

    public void ModifyGold(int delta)
    {
        this.gold += delta;
    }

    public Vector3 GetWallPosition()
    {
        if (_wall == null)
            return Vector3.zero;
        return _wall.transform.position;
    }

    override public Transform GetWeaponNode()
    {
        if (weaponNode != null) 
            return weaponNode;

        return this.transform;
    }

    public Vector3 GetFirePos()
    {
        var weaponNode = this.GetWeaponNode();
        return weaponNode.position;
    }

    public void SetChangeSeatTip(int time)
    {
        if (changeSeatTip != null)
        {

            changeSeatTip.gameObject.SetActive(time != 0);
            if (time > 0)
            {
                Timer.Add(time, 1, HideSeatTip, changeSeatTip);
            }
        }
    }

    private void HideSeatTip()
    {
        if (changeSeatTip != null)
        {
            changeSeatTip.gameObject.SetActive(false);
        }
    }

    public void RemoveSeatTipTimer(){
        Timer.Remove(changeSeatTip, HideSeatTip);
    }

    /// <summary>
    /// 初始化玩家对象
    /// </summary>
    public void Init(List<BattleAttr>  battleAttrList)
    {
        Log.Info("=========Battle Attr Init==========");
        for (int i = 0; i < battleAttrList.Count; i++) {
            var attr = battleAttrList[i];
            // if (attr.Type == BattleAttrType.HpValue.GetHashCode()) {
            //     // Log.Info("HpValue:" + attr.Value);
            //     this.maxHp = attr.Value;
            // }
            // else if (attr.Type == BattleAttrType.AtkValue.GetHashCode()) {
            //     // Log.Info("AtkValue:" + attr.Value);
            //     this.attack = attr.Value;
            // } 
            Log.Info(((BattleAttrType)attr.Type) + ":" + attr.Value);
            this.AddAttrValue((BattleAttrType)attr.Type, attr.Value);   
        }
        this.UpdateBaseAttr();
        Log.Info("=======================");
    }

    protected void UpdateBaseAttr()
    {
        this.maxHp = GetAttrValue(BattleAttrType.HpValue);      //基础最大血量
        this.attack = GetAttrValue(BattleAttrType.AtkValue);    //基础攻击

        if (curHp <= 0){
            curHp = GetMaxHp();
        }
        this.hpBar.SetProgress(curHp, GetMaxHp());
    }

    /// <summary>
    /// 等级提升
    /// </summary>
    public void LevelUp(bool isLevelUp)
    {
        if (isLevelUp)
        {
            var nextLevel = level + 1;
            var levelInfo = ConfigLevel.GetData(nextLevel);
            if (levelInfo != null)
            {
                level = nextLevel;
            }
        }
      
        if (this._pet != null) {
            this._pet.CheckLevelUp();
        }
        if (this._artifact != null) {
            this._artifact.CheckLevelUp();
        }
        UpdateNextExp();
    }
    /// <summary>
    /// 增加经验
    /// </summary>
    protected void UpdateNextExp()
    {
        curExp -= maxExp;
        if (curExp < 0)
            curExp = 0;

        var nextLevel = level + 1;
        var levelInfo = ConfigLevel.GetData(nextLevel);
        if (levelInfo != null) {
            maxExp = levelInfo.exp;
        } else {
            //达到最大等级
            curExp = 0;
        }
    }

    /// <summary>
    /// 获取最近目标
    /// </summary>
    /// <returns></returns>
    override public Vector3? GetNearestTargetPos()
    {
        var monster = BattleUnitManager.Inst.GetNearestMonster(transform.position);
        if (monster == null)
            return null;
        return monster.transform.position;
    }
    /// <summary>
    /// 获取视野范围内的随机目标(这个是有确定目标)点
    /// </summary>
    /// <returns></returns>
    override public Vector3? GetRandomTargetPosInEype()
    {
        var monster = BattleUnitManager.Inst.GetRandomMonster(this.GetFirePos());
        if (monster == null)
            return null;
        return monster.transform.position;
    }

    /// <summary>
    /// 获取视野范围内随机点
    /// </summary>
    /// <returns></returns>
    override public Vector3? GetRandomPosInEype()
    {
        return bulletRandomArea.GetRandomPos();
        // float board = 1;
        // var halfCameraWidth = BattleCamera.Inst.halfCameraWidth - board;
        // var halfCameraHeight = BattleCamera.Inst.halfCameraHeight - board;
        // var randX = UnityEngine.Random.Range(-halfCameraWidth, halfCameraWidth);
        // var randY = UnityEngine.Random.Range(-halfCameraHeight, halfCameraHeight);
        // return new Vector3(randX, randY);
    }

    /// <summary>
    /// 按优先级选怪：boss，精英怪，小怪
    /// </summary>
    /// <returns></returns>
    public override Vector3? GetMaxTypeMonster()
    {
        var monster = BattleUnitManager.Inst.GetMaxTypeMonster(this.GetFirePos());
        if (monster == null)
            return null;
        return monster.transform.position;
    }

    public Vector3 GetItemDropPosInEype(Vector3 pos, float offsetX, float offsetY)
    {
        return bulletRandomArea ? bulletRandomArea.GetPosInEype(pos, offsetX, offsetY) : pos;
    }

    /// <summary>
    /// 获取区域外左右两边随机点
    /// </summary>
    /// <param name="expand">向外扩展多少个单位</param>
    /// <returns></returns>
    public Vector3 GetRandomOuterHorizontalPos(float expand = 1)
    {
        return bulletRandomArea.GetRandomOuterHorizontalPos(expand);
    }

    /// <summary>
    /// 获取本对象朝向的目标点
    /// </summary>
    /// <returns></returns>
    override public Vector3? GetTargetPosByDirection() 
    {
        var dir = this.GetDirection();
        if (dir.x == 0 && dir.y == 0)
        {
            return Vector3.zero;
        }
        return transform.position + dir * 2;
    }


    override public Transform GetFollowNode(int followIndex)
    {
        if (followIndex < followNodes.Length)
        {
            return followNodes[followIndex];
        }
        return null;
    }

    override protected void OnUpdate()
    {
        //elapsedTime += Time.deltaTime;

        //掉落拾取检查
        CheckPickUpItem();
        //英雄拖拽
        CheckDragHero();

        hpBar.SetProgress(curHp, GetMaxHp());

        hpBar.SetShieldHp(curShieldHp, maxShieldHp);
        if (curShieldHp <= 0 && maxShieldHp > 0)
        {
            _wall.ShowShield(false);
        }
    }

    /// <summary>
    /// 道具拾取
    /// </summary>
    private void CheckPickUpItem()
    {
        if (!Input.GetMouseButtonDown(0) || Stage.isTouchOnUI)
        {
            return;
        }
        var camera = BattleCamera.Inst.GetMainCamera();
        Vector2 pos = camera.ScreenToWorldPoint(Input.mousePosition);
        var hit = Physics2D.Raycast(pos, -Vector2.up, 0f, LayerMaskPickup);
        if (hit.collider != null)
        {
            var dropItem = hit.collider.gameObject.GetComponent<DropItem>();
            if (dropItem != null) {
                var dropItems = BattleUnitManager.Inst.GetDropItems(DropItemType.SelectAddEffectSkill, dropItem);
                if(dropItem.info != null)
                {
                    dropItems.Insert(0, dropItem);  //点击的放在最前面
                }
                for (var i = 0; i < dropItems.Count; i++) {
                    var item = dropItems[i];
                    Timer.Add(0.3f * i, 1, () =>
                    {
                        item.FlyTo(this);

                    }, this, false);
                }

            }
        }
    }

    /// <summary>
    /// 英雄拖拽
    /// </summary>
    private void CheckDragHero()
    {
        if (_dragStartPos != null)
        {
            if (Input.GetMouseButtonUp(0))
            {
                //取消拖动
                OnDropHero();
            }
            else
            {
                //拖动中
                OnDragHero();
            }
        }
        else
        {
            //判断是否选中了英雄
            CheckStartDragHero();
        }
    }
    private void OnDragHero()
    {
        if (_dragStartPos == null)
            return;

        var dragHero = _dragStartPos.GetStandHero();
        if (dragHero == null)
            return;
        var camera = BattleCamera.Inst.GetMainCamera();
        Vector2 pos = camera.ScreenToWorldPoint(Input.mousePosition);
        dragHero.transform.position = pos;
        _attackRange.position = pos;

        //如果位置划过座位，则座位需要发更多的光
        var hit = Physics2D.Raycast(pos, -Vector2.up, 0f, LayerHeroPos);
        if (hit.collider != null)
        {
            var hitObj = hit.collider.gameObject;
            var targetPlacePos = hitObj.GetComponent<PlacePos>();
            if (targetPlacePos != null)
            {
                targetPlacePos.ShowHighLight(true);
            }
        }
        else
        {
            //所有的位置需要发光
            foreach (var placePosShow in heroNodes)
            {
                if (placePosShow.IsHighLight)
                {
                    placePosShow.ShowHighLight(false);
                    break;
                }
            }
        }
    }

    private void OnDropHero()
    {
        //所有的位置需要发光
        foreach (var placePos in heroNodes)
        {
            placePos.Show(false);
        }
        if (Session.GuideId > 0)
        {
            var guide = ConfigGuide.GetData(Session.GuideId);
            if (guide != null && guide.type == 7)
            {
                Session.GuideId = guide.nextId;
                NotifyMgr.Event(NotifyNames.CheckGuide);
            }
        }
        //则判断是否在指定位置，没在指定位置需要将英雄还原
        var camera = BattleCamera.Inst.GetMainCamera();
        Vector2 pos = camera.ScreenToWorldPoint(Input.mousePosition);

        var hit = Physics2D.Raycast(pos, -Vector2.up, 0f, LayerHeroPos);
        if (hit.collider != null)
        {
            var hitObj = hit.collider.gameObject;
            var targetPlacePos = hitObj.GetComponent<PlacePos>();
            if (targetPlacePos != null)
            {
                var targetHero = targetPlacePos.GetStandHero();
                var dragHero = _dragStartPos.GetStandHero();
                dragHero.enbaleFire = true;
                _dragStartPos.SetStandHero(targetHero);
                targetPlacePos.SetStandHero(dragHero);
            }
        }
        else
        {
            var dragHero = _dragStartPos.GetStandHero();
            dragHero.enbaleFire = true;
            this._dragStartPos.SetStandHero(dragHero);
        }
        this._dragStartPos = null;

        if (_attackRange.gameObject.activeSelf)
        {
            _attackRange.gameObject.SetActive(false);
        }
    }

    private void CheckStartDragHero()
    {
        if (!Input.GetMouseButtonDown(0) || Stage.isTouchOnUI)
        {
            return;
        }

        if (_dragStartPos != null)
        {
            return;
        }

        var camera = BattleCamera.Inst.GetMainCamera();
        Vector2 pos = camera.ScreenToWorldPoint(Input.mousePosition);

        var hit = Physics2D.Raycast(pos, -Vector2.up, 0f, LayerHeroPos);
        if (hit.collider != null)
        {
            var hitObj = hit.collider.gameObject;
            if (hitObj == null)
                return;

            var placePosStart = hitObj.GetComponent<PlacePos>();
            if (placePosStart != null)
            {
                var hero = placePosStart.GetStandHero();
                if (null == hero)
                    //没有占位，不拖拽
                    return;

                if (hero.GetHeroType() != HeroType.Common)
                    //类型不匹配
                    return;
                if(Session.GuideId > 0)
                {
                    var guide = ConfigGuide.GetData(Session.GuideId);
                    if(guide != null && guide.type == 7)
                    {
                        Session.GuideId = guide.nextId;
                        NotifyMgr.Event(NotifyNames.CheckGuide);
                    }
                }
                hero.enbaleFire = false;
                //开始拖拽
                _dragStartPos = placePosStart;
                placePosStart.ShowLevelTxt(false);
                if (!_attackRange.gameObject.activeSelf)
                {
                    _attackRange.gameObject.SetActive(true);
                    _attackRange.position = hero.transform.position;
                    //设置攻击半径
                    float attackRange = hero.GetAttackRange();
                    if (attackRange > 0f)
                    {
                        _attackRange.localScale = Vector3.one * attackRange / 10f;
                    }
                    else
                    {
                        _attackRange.localScale = Vector3.one;
                    }
                }
                //所有的位置需要发光
                foreach (var placePosShow in heroNodes)
                {
                    placePosShow.Show(true);
                }
            }
        }
    }

    override public bool IsIgnoreBuff(InfoBuff infoBuff)
    {
        bool ignore = false;
        //免疫判断
        switch ((BuffEffectType)infoBuff.effect)
        {
            case BuffEffectType.StopStatus:
                ignore = GetAttrValue(BattleAttrType.StopIgnore) > 0;
                break;
            case BuffEffectType.PalsyStatus:
                ignore = GetAttrValue(BattleAttrType.PalsyIgnore) > 0;
                break;
            case BuffEffectType.FrozenStatus:
                ignore = GetAttrValue(BattleAttrType.FrozenIgnore) > 0;
                break;
            case BuffEffectType.VertigoStatus:
                ignore = GetAttrValue(BattleAttrType.VertigoIgnore) > 0;
                break;
        }

        return ignore;
    }

    static private string ColorText(string text, string hexColor)
    {
        return "<color=" + hexColor + ">" + text + "</color>";
    }
    static private string ColorText(string text, Color color)
    {
        return "<color=#" + ColorUtility.ToHtmlStringRGB(color) + ">" + text + "</color>";
    }


    /// <summary>
    /// 使用技能
    /// </summary>
    /// <param name="skillId"></param>
    /// <returns></returns>
    override public bool UseSkill(int skillId)
    {
        var infoSkill = ConfigSkill.GetData(skillId);
        if (null == infoSkill)
        {
            return false;
        }

        //英雄的关联技能在英雄上
        IBattleObject user = this;
        if (infoSkill.relatedHeroId != 0)
        {
            var hero = FindHero(infoSkill.relatedHeroId);
            if (null == hero)
            {
                //新建一个英雄
                hero = BattleUnitManager.Inst.FindHeroByHeroId(infoSkill.relatedHeroId);
                this.AddHero(hero);
            }

            if (hero != null)
            {
                user = hero;
            }

            var heroPlacePos = GetPlacePosByHeroId(hero.GetHeroId());
            if(heroPlacePos != null)
            {
                heroPlacePos.UpdateHeroLevelTxt();
            }

            if (!infoSkill.IsUniqueSkill())
            {
                //同步装备必杀技
                var uniqueSkillId = FindUniqueSkillByHeroId(infoSkill.relatedHeroId, infoSkill.level);
                if (uniqueSkillId != 0)
                {
                    EquipUniqueSkill(hero, uniqueSkillId);
                }
            }
        }

        if (!SkillSystem.UseSkill(infoSkill, user)) {
            return false;
        }
        if (IsSkillUseNeedRecord(infoSkill)) {
             _battleProgressSkills.Add(skillId);
        }
        Log.Debug("Player use skill {0} sucesss", skillId);

        ComputeReduceHarmPercent();
        return true;
    }


    /// <summary>
    /// 初始化可用的必杀技
    /// </summary>
    /// <param name="skillIds"></param>
    public void InitUniqueSkills(List<int> skillIds)
    {
        uniqueSkills = new List<int>(skillIds);
    }

    /// <summary>
    /// 获取英雄关联的必杀技，没有则返回0
    /// </summary>
    /// <param name="heroId"></param>
    /// <returns></returns>
    public int FindUniqueSkillByHeroId(int heroId, int skillLevel)
    {
        for (int i = 0; i < uniqueSkills.Count; i++)
        {
            var info = ConfigSkill.GetData(uniqueSkills[i]);
            if (info == null || info.relatedHeroId != heroId)
                continue;

            if (info.level == skillLevel)
            {
                return info.id;
            }

            for (int j = 0; j < 20; j++)
            {
                if (info.nextId <= 0)
                    break;
                info = ConfigSkill.GetData(info.nextId);
                if (info.level == skillLevel)
                {
                    return info.id;
                }
            }
        }
        return 0;
    }


    /// <summary>
    /// 装备必杀技
    /// </summary>
    /// <param name="uniqueSkillId"></param>
    /// <param name="hero"></param>
    private void EquipUniqueSkill(Hero hero, int uniqueSkillId)
    {
        var uniqueSkillInfo = ConfigSkill.GetData(uniqueSkillId);
        if (uniqueSkillInfo != null)
        {
            equipedUniqueSkillDic[uniqueSkillInfo.relatedHeroId] = uniqueSkillId;
            SkillSystem.UseSkill(uniqueSkillInfo, hero);
        }
    }

    /// <summary>
    /// 获取已装备的必杀技
    /// </summary>
    /// <returns></returns>
    public List<int> GetEquipedUniqueSkills()
    {
        var equipedUniqueSkills = new List<int>();
        foreach (var value in equipedUniqueSkillDic.Values)
        {
            equipedUniqueSkills.Add(value);
        }
        return equipedUniqueSkills;
    }

    /// <summary>
    /// 使用必杀技
    /// </summary>
    /// <param name="uniqueSkillId"></param>
    public void UseUniqueSkill(int heroId, int uniqueSkillId)
    {
        var hero = BattleUnitManager.Inst.FindHeroByHeroId(heroId);
        if (hero == null)
            return;

        var infoSkill = ConfigSkill.GetData(uniqueSkillId);
        if (infoSkill == null)
            return;

        int weaponId = (int)infoSkill.effectParam0;
        var infoWeapon = ConfigWeapon.GetData(weaponId);
        if (infoWeapon == null)
            return;

        var weapon = hero.GetWeapon(infoWeapon.weaponGroup);
        if (weapon == null)
            return;

        var emitor = weapon.GetEmitor();
        if (emitor == null)
            return;
        emitor.ManualFire();
    }


    /// <summary>
    /// 角色使用这个技能是否需要记录，直接加属性的无需记录
    /// </summary>
    /// <returns></returns>
    public static bool IsSkillUseNeedRecord(InfoSkill infoSkill)
    {
        switch ((SkillEffectType)infoSkill.effectType) {
            case SkillEffectType.EquipWeapon:
            case SkillEffectType.AddBuff:
            case SkillEffectType.AddBattleBuff:
                return true;
            default:
                return false;
        }
    }

    /// <summary>
    /// 战斗记录相关的技能，不是所有技能都需要还原，有的技能是加属性，加血，还原了会导致重复使用
    /// </summary>
    /// <returns></returns>
    public List<int> GetProgressSkillList()
    {
        return _battleProgressSkills;
    }

    /// <summary>
    /// 获取所有攻击类型的英雄，包含英雄塔
    /// </summary>
    /// <param name="heroAtkType"></param>
    /// <returns></returns>
    public List<Hero> GetHeroesByAtkType(int heroAtkType)
    {
        var list = new List<Hero>();
        foreach (var hero in _heroDict.Values)
        {
            if (heroAtkType > 0 && heroAtkType != hero.GetAttackType())
            {
                //职业不匹配
                continue;
            }
            list.Add(hero);
        }
        return list;
    }


    /// <summary>
    /// 获取所有职业的英雄，包含英雄塔
    /// </summary>
    /// <param name="heroClass"></param>
    /// <returns></returns>
    public List<Hero> GetHeroesByClass(int heroClass)
    {
        var list = new List<Hero>();
        foreach (var hero in _heroDict.Values)
        {
            if (heroClass > 0 && heroClass != hero.GetClasses())
            {
                //职业不匹配
                continue;
            }
            list.Add(hero);
        }
        return list;
    }

    /// <summary>
    /// 获取英雄的VO数据列表
    /// </summary>
    /// <returns></returns>
    public List<HeroDataVo> GetHeroDataList()
    {
        var list = new List<HeroDataVo>();
        foreach (var hero in _heroDict.Values)
        {
            list.Add(hero.GetHeroDataVo());
        }
        return list;
    }

    /// <summary>
    /// 查找已经上阵的英雄
    /// </summary>
    /// <param name="heroId"></param>
    /// <returns></returns>
    public Hero FindHero(int heroId)
    {
        _heroDict.TryGetValue(heroId, out var hero);
        return hero;
    }

    /// <summary>
    /// 添加英雄
    /// </summary>
    /// <param name="hero"></param>
    protected void AddHero(Hero hero)
    {
        if (null == hero)
            return;

        _heroDict.TryGetValue(hero.GetHeroId(), out var oldHero);
        if (oldHero != null)
            return;

        var heroType = hero.GetHeroType();
        PlacePos placePos = null;
        if (heroType == HeroType.Common) {
            placePos = GetEmptyHeroPlacePos();
        } else if (heroType == HeroType.Tower) {
            placePos = GetEmptyHeroTowerPlacePos();
        } else {
            return;
        }

        if (null == placePos)
            return;

        placePos.SetStandHero(hero);
        hero.gameObject.SetActive(true);

        _heroDict.Add(hero.GetHeroId(), hero);
    }

    /// <summary>
    /// 是否有放置位置
    /// </summary>
    /// <param name="heroType"></param>
    /// <returns></returns>
    public bool HaveHeroPlacePos(HeroType heroType)
    {
        PlacePos placePos = null;
        if (heroType == HeroType.Common)
        {
            placePos = GetEmptyHeroPlacePos();
        }
        else
        {
            placePos = GetEmptyHeroTowerPlacePos();
        }
        return placePos != null;
    }

    /// <summary>
    /// 获取一个空缺的英雄站位
    /// </summary>
    /// <returns></returns>
    public PlacePos GetEmptyHeroPlacePos()
    {
        if (heroNodes == null)
            return null;

        foreach (var heroNode in heroNodes)
        {
            if (heroNode.GetStandHero() == null)
                return heroNode;
        }
        return null;
    }

    /// <summary>
    /// 获取英雄站位数量
    /// </summary>
    /// <returns></returns>
    public int GetHeroPlacePosCount()
    {
        return heroNodes.Length;
    }

    /// <summary>
    /// 获取站位上的英雄
    /// </summary>
    /// <param name="index"></param>
    /// <returns></returns>
    public Hero GetPlacePosHero(int index = 0)
    {
        if (index < 0 || index >= heroNodes.Length) {
            return null;
        }
        return heroNodes[index].GetStandHero();
    }

     /// <summary>
    /// 获取站位
    /// </summary>
    /// <param name="index"></param>
    /// <returns></returns>
    public PlacePos GetPlacePos(int index = 0)
    {
        if (index < 0 || index >= heroNodes.Length) {
            return null;
        }
        return heroNodes[index];
    }

    public PlacePos GetPlacePosByHeroId(int heroId = 0)
    {
       foreach (var heroNode in heroNodes)
        {
            if(heroNode.GetStandHero() != null && heroNode.GetStandHero().GetHeroId() == heroId)
            {
                return heroNode;
            }
        }
        return null;
    }

    /// <summary>
    /// 获取一个空缺的英雄塔站位
    /// </summary>
    /// <returns></returns>
    public PlacePos GetEmptyHeroTowerPlacePos()
    {
        if (heroTowerNodes == null)
            return null;

        foreach (var heroNode in heroTowerNodes)
        {
            if (heroNode.GetStandHero() == null)
                return heroNode;
        }
        return null;
    }

    public long GetTotalDamage()
    {
        long totalDamage = 0;
        return totalDamage;
    }
    

    public override void AddHp(int value, bool displayAddValue = true)
    {
        if (value <= 0)
            return;

        var deltaValue = value;
        long curMaxHp = GetMaxHp();
        if (curHp + value > curMaxHp)
        {
            deltaValue = (int)(curMaxHp - curHp);
        }

        curHp += deltaValue;

        if (displayAddValue && value > 0)
        {
            SoundManager.PlayEffect(SoundPath.recovery);
            FlyEffect.HurtNumByPos(GetWallFlyNumPos(), Mathf.FloorToInt(value), Color.green, 1);
        }
    }

    public override void SetShieldHp(int curHp, int maxHp)
    {
        if (_wall != null)
        {
            _wall.ShowShield(true);
        }

        if (curShieldHp != 0)
            return;//已经从进度中还原不再设置

        this.curShieldHp = curHp;
        this.maxShieldHp = maxHp;
    }

    public long GetShieldHp()
    {
        return curShieldHp;
    }

    internal void Idle()
    {
        if (isDie) 
            return;
    }

    /// <summary>
    /// 0:top 1:right 2:bottom 3:left
    /// </summary>
    Vector2[] hitBoardPosAry = new Vector2[4];
    bool[] hitBoards = new bool[4];
    
    public bool IsHitTop { get { return hitBoards[0]; } }
    public bool IsHitRight { get { return hitBoards[1]; } }
    public bool IsHitBottom { get { return hitBoards[2]; } }
    public bool IsHitLeft { get { return hitBoards[3]; } }
    
    public float TopBoardPos { get { return hitBoardPosAry[0].y; } }
    public float RightBoardPos { get { return hitBoardPosAry[1].x; } }
    public float BottomBoardPos { get { return hitBoardPosAry[2].y; } }
    public float LeftBoardPos { get { return hitBoardPosAry[3].x; } }

    [SerializeField]
    public float outerOffset = 1;

    override public Vector2 GetHitBorderNoraml(ref Vector3 targetPos)
    {
        var right = IsHitRight ? hitBoardPosAry[1].x : hitBoardPosAry[1].x - outerOffset;
        if (targetPos.x > right)
        {
            targetPos.x = right;
            return Vector2.left;
        }

        var left = IsHitLeft ? hitBoardPosAry[3].x : hitBoardPosAry[3].x + outerOffset;
        if (targetPos.x < left)
        {
            targetPos.x = left;
            return Vector2.right;
        }

        var top = IsHitTop ? hitBoardPosAry[0].y : hitBoardPosAry[0].y - outerOffset;
        if (targetPos.y > top)
        {
            targetPos.y = top;
            return Vector3.down;
        }
        
        var bottom = IsHitBottom ? hitBoardPosAry[2].y : hitBoardPosAry[2].y + outerOffset;
        if (targetPos.y < bottom)
        {
            targetPos.y = bottom;
            return Vector3.up;
        }
        return Vector2.zero;
    }

    /// <summary>
    /// 获取攻击
    /// </summary>
    /// <returns></returns>
    override public float GetAttackValue()
    {
        float addPercent = GetAtkddPercent();
        float curAtkValue = attack * (1f + addPercent);
        return curAtkValue;
    }
    
    protected float GetAtkddPercent()
    {
        return this.GetAttrPercent(BattleAttrType.AtkAddPercent);
    }


    Collider2D[] pickUps = new Collider2D[20];
    /*
    private void CheckPickUp()
    {
        if (Time.frameCount % 4 != 0)
            return;

        var hitCount = Physics2D.OverlapCircleNonAlloc(transform.position, pickUpRange, pickUps, LayerMaskPickup);
        for (int i = 0; i < hitCount; i++)
        {
            var collider = pickUps[i];
            if (collider != null)
            {
                var dropItem = collider.GetComponent<DropItem>();
                if(dropItem != null && !dropItem.IsFlying)
                {
                    dropItem.FlyTo(this);
                }
            }
        }
    }

   */


    private void OnCollisionStay2D(Collision2D collision)
    {
        if (this.isDie)
            return;

        if (collision.gameObject.CompareTag(TagNames.Enemy))
        {
            var monster = collision.gameObject.GetComponent<Monster>();
            monster.OnCollisionTarget(this);
        }
    }

    internal void CollectExp(int value)
    {
        if (IsLevelMax()) {
            return;
        }
        curExp += value;
    }

    public bool IsLevelMax()
    {
        // var nextLevel = this.level + 1;
        // var levelInfo = ConfigLevel.GetData(nextLevel);
        return this.level >= ConfigLevel.maxLevel || BattleSkillPool.Inst.RandomAtkSkill().Count <= 0;
    }

    internal void CollectItem(InfoDropItem dropInfo)
    {
        OnCollectItem?.Invoke(dropInfo);
    }

    public Vector3 GetWallFlyNumPos()
    {
        var pos = hpBar.transform.position;
        return new Vector3(pos.x, pos.y + 0.5f, pos.z);
    }

    public float GetReduceHarmPercent()
    {
        return _reduceHarmPercent;
    }

    private InfoSkill counterattackSkillInfo;
    public override void SetCounterattack(int skillId)
    {
        base.SetCounterattack(skillId);
        counterattackSkillInfo = ConfigSkill.GetData(skillId);
    }

    public override void OnBulletHit(BulletBase bullet)
    {
        base.OnBulletHit(bullet);
        if (counterattackSkillInfo != null)
        {
            var weaponOwner = bullet.GetWeaponOwner();
            if (weaponOwner != null)
            {
                int percent = counterattackSkillInfo.effectParam0;
                var atkValue = GetAttackValue();
                int damage = (int)Math.Round((double)atkValue * percent / 100f);

                bool isBoss = false;
                if (weaponOwner is Monster && (weaponOwner as Monster).GetMonsterType() == (int)MonsterType.Boss)
                {
                    isBoss = true;
                }

                BattleSkillPool.AddSkillDamage(counterattackSkillInfo.link, (long)damage, isBoss);
                weaponOwner.OnHurt(damage, 100);
            }
        }
    }

    public override void OnHurt(int damage, float damageMultiplier = 1f, string hitEffect = null)
    {
        if (isDie)
            return;

        if (damage == 0)
            return;

        //减伤
        var reduceHarmValue = damage * GetReduceHarmPercent();
        damage -= (int)reduceHarmValue;
        if (damage <= 0) {
            damage = 1;
        }
        
        //Platform.GetInstance().Vibrate();

        if (_unHurt)
            return;

        if (_wall != null)
        {
            _wall.PlayHitFlashEffect();
        }

        //EffectFactory.Create("PlayerHit", transform.position, 0.9f);
        SoundManager.PlayEffect(SoundPath.playerHit);

        FlyEffect.HurtNumByPos(this.GetWallFlyNumPos(), damage, Color.red, 1.5f);

        if (curShieldHp > 0)
        {
            if (curShieldHp >= damage)
            {
                curShieldHp -= damage;
            }
            else
            {
                damage -= (int)curShieldHp;
                curShieldHp = 0;
                curHp -= damage;
            }
        }
        else
        {
            curHp -= damage;
        }

        if (curHp <= 0) {
            curHp = 0;
            Die();
        }
        if (GetMaxHp() > 0)
        {
            double progress = (double)curHp / GetMaxHp();
            NotifyMgr.Event(NotifyNames.PlayerUnderAttack, progress);
        }
    }


    async private void UnHurt(float unHurtTime)
    {
        _unHurt = true;
        await UniTask.Delay((int)(unHurtTime*1000));
        _unHurt = false;
    }

    async private void Die()
    {
        if (isDie) {
            return;
        }

        isDie = true;
        var heroList = BattleUnitManager.Inst.GetHeros();
        foreach (var hero in heroList.Values) {
            hero.enbaleFire = false;
        }

        if (_aniWallBoom != null) {
            _aniWallBoom.gameObject.SetActive(true);
            _aniWallBoom.Play(AniNames.Default, false);
            SoundManager.PlayEffect(SoundPath.ui_wallWorn);
        }

        await UniTask.Delay((int)400);
        if (this == null) {
            return;
        }

        _wall?.Worn();

        await UniTask.Delay((int)1200);
        if (this == null) {
            return;
        }

        OnDieAction?.Invoke(this);
    }

    override protected void OnDestroy()
    {
        if (debugGo != null)
        {
            GameObject.Destroy(debugGo);
        }

        base.OnDestroy();
    }

    public void PreStartBattle(BattleProgressData recoverData)
    {
        if (recoverData != null) {
            this.curHp = recoverData.CurHp;
        } else {
             this.curHp = this.GetMaxHp();
        }
    }

    async public void Reborn(float hpRecoverPercent)
    {
        if (_aniRepair != null) {
            _aniRepair.gameObject.SetActive(true);
            _aniRepair.Play(AniNames.Default);
            SoundManager.PlayEffect(SoundPath.ui_wallFix);
            await UniTask.Delay((int)500);
            if (this == null) {
                return;
            }

            _wall?.UnWorn();
        }
        await UniTask.Delay((int)350);
        if (this == null) {
            return;
        }
        
        this.curHp = (int)(hpRecoverPercent * this.GetMaxHp());

        await UniTask.Delay((int)600);
        if (this == null) {
            return;
        }

        isDie = false;
        var heroList = BattleUnitManager.Inst.GetHeros();
        foreach (var hero in heroList.Values) {
            hero.enbaleFire = true;
        }
    }


#if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        UnityEditor.Handles.color = Color.red;

        UnityEditor.Handles.color = Color.green;
        UnityEditor.Handles.DrawWireDisc(transform.position, Vector3.back, maxFarAway);
        
        Gizmos.color = Color.yellow;
        for (int i = 0; i < 4; i++)
        {
            Gizmos.DrawLine(transform.position, hitBoardPosAry[i]);
        }
    }
#endif
}