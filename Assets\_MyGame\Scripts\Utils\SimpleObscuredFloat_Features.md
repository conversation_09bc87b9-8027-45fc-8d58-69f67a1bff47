# SimpleObscuredFloat 特性说明

## 概述

SimpleObscuredFloat 是专门为浮点数设计的加密保护类，使用位级联合体技术实现 float 和 int 之间的无损转换，然后进行 XOR 加密。

## 核心技术

### 1. 位级联合体 (FloatIntUnion)

```csharp
[StructLayout(LayoutKind.Explicit)]
private struct FloatIntUnion
{
    [FieldOffset(0)] public float floatValue;
    [FieldOffset(0)] public int intValue;
}
```

**作用**：
- 允许 float 和 int 在内存中占用相同位置
- 实现无损的位级转换
- 保持浮点数的精确表示

### 2. 加密解密算法

```csharp
// 加密：float → int → XOR
public static int Encrypt(float value, int key)
{
    var union = new FloatIntUnion { floatValue = value };
    return union.intValue ^ key;
}

// 解密：XOR → int → float
public static float Decrypt(int value, int key)
{
    var union = new FloatIntUnion { intValue = value ^ key };
    return union.floatValue;
}
```

## 浮点数特殊处理

### 1. 精度比较

由于浮点数的精度问题，相等比较使用 `Mathf.Approximately`：

```csharp
private static bool Compare(float a, float b)
{
    return Mathf.Approximately(a, b);
}

public static bool operator ==(SimpleObscuredFloat a, SimpleObscuredFloat b)
{
    return Compare(a.InternalDecrypt(), b.InternalDecrypt());
}
```

### 2. 大小比较的精度处理

```csharp
public static bool operator >=(SimpleObscuredFloat a, SimpleObscuredFloat b)
{
    var aVal = a.InternalDecrypt();
    var bVal = b.InternalDecrypt();
    return aVal > bVal || Compare(aVal, bVal); // 大于 或 近似相等
}
```

## 使用示例

### 基本操作

```csharp
// 声明和初始化
SimpleObscuredFloat speed = 5.5f;
SimpleObscuredFloat damage = 12.8f;

// 算术运算
speed += 1.5f;              // 加法
damage *= 1.2f;             // 乘法
float totalDamage = damage * speed; // 混合运算

// 比较运算
if (speed > 5.0f)
{
    Debug.Log("速度超过5.0");
}

if (Mathf.Approximately(damage, 15.36f))
{
    Debug.Log("伤害约等于15.36");
}
```

### 游戏中的实际应用

```csharp
public class PlayerStats : MonoBehaviour
{
    [Header("玩家属性")]
    [SerializeField] private SimpleObscuredFloat moveSpeed = 5.0f;
    [SerializeField] private SimpleObscuredFloat attackSpeed = 1.5f;
    [SerializeField] private SimpleObscuredFloat criticalRate = 0.15f;
    [SerializeField] private SimpleObscuredFloat damageMultiplier = 1.0f;
    
    public void UpdateMovement()
    {
        // 使用加密的移动速度
        float currentSpeed = moveSpeed;
        transform.Translate(Vector3.forward * currentSpeed * Time.deltaTime);
    }
    
    public bool TryCriticalHit()
    {
        // 使用加密的暴击率
        return UnityEngine.Random.value < criticalRate;
    }
    
    public float CalculateDamage(float baseDamage)
    {
        // 使用加密的伤害倍率
        float finalDamage = baseDamage * damageMultiplier;
        
        if (TryCriticalHit())
        {
            finalDamage *= 2.0f; // 暴击双倍伤害
        }
        
        return finalDamage;
    }
}
```

## 性能考虑

### 1. 加密开销

```csharp
// 性能测试示例
void PerformanceTest()
{
    const int iterations = 100000;
    
    // 普通 float
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();
    float normalSum = 0f;
    for (int i = 0; i < iterations; i++)
    {
        normalSum += i * 0.1f;
    }
    stopwatch.Stop();
    long normalTime = stopwatch.ElapsedMilliseconds;
    
    // SimpleObscuredFloat
    stopwatch.Restart();
    SimpleObscuredFloat obscuredSum = 0f;
    for (int i = 0; i < iterations; i++)
    {
        obscuredSum += i * 0.1f;
    }
    stopwatch.Stop();
    long obscuredTime = stopwatch.ElapsedMilliseconds;
    
    Debug.Log($"性能比率: {(float)obscuredTime / normalTime:F2}x");
}
```

### 2. 优化建议

```csharp
// 不推荐：频繁访问
for (int i = 0; i < 1000; i++)
{
    if (playerSpeed > 5.0f) // 每次都解密
    {
        // 处理逻辑
    }
}

// 推荐：缓存解密值
float currentSpeed = playerSpeed; // 只解密一次
for (int i = 0; i < 1000; i++)
{
    if (currentSpeed > 5.0f)
    {
        // 处理逻辑
    }
}
```

## 注意事项

### 1. 浮点精度

```csharp
SimpleObscuredFloat a = 0.1f;
SimpleObscuredFloat b = 0.2f;
SimpleObscuredFloat c = 0.3f;

// 可能为 false，因为浮点精度问题
bool isEqual = (a + b == c);

// 推荐使用 Mathf.Approximately
bool isApproximately = Mathf.Approximately(a + b, c);
```

### 2. 特殊值处理

```csharp
// 特殊浮点值
SimpleObscuredFloat nan = float.NaN;
SimpleObscuredFloat infinity = float.PositiveInfinity;
SimpleObscuredFloat negInfinity = float.NegativeInfinity;

// 检查特殊值
if (float.IsNaN(playerSpeed))
{
    playerSpeed = 5.0f; // 重置为默认值
}
```

### 3. 序列化兼容性

```csharp
[System.Serializable]
public class GameConfig
{
    [Header("游戏平衡参数")]
    public SimpleObscuredFloat playerBaseSpeed = 5.0f;
    public SimpleObscuredFloat enemySpawnRate = 2.0f;
    public SimpleObscuredFloat difficultyMultiplier = 1.0f;
    
    // Unity Inspector 中会显示解密后的值
    // 保存时会自动序列化加密数据
}
```

## 最佳实践

### 1. 适用场景

**推荐使用**：
- 玩家移动速度、攻击速度
- 伤害倍率、暴击率
- 游戏平衡参数
- 重要的浮点配置值

**不推荐使用**：
- 临时计算变量
- UI 动画参数
- 频繁变化的物理参数

### 2. 代码组织

```csharp
public class PlayerController : MonoBehaviour
{
    [Header("核心属性 - 加密保护")]
    [SerializeField] private SimpleObscuredFloat baseSpeed = 5.0f;
    [SerializeField] private SimpleObscuredFloat jumpForce = 10.0f;
    
    [Header("临时属性 - 普通数值")]
    [SerializeField] private float currentVelocity;
    [SerializeField] private float animationSpeed;
    
    private void Update()
    {
        // 缓存重要数值
        float speed = baseSpeed;
        float jump = jumpForce;
        
        // 使用缓存值进行计算
        HandleMovement(speed);
        HandleJump(jump);
    }
}
```

### 3. 调试友好

```csharp
public void DebugPlayerStats()
{
    Debug.Log($"移动速度: {moveSpeed.ToString("F2")}");
    Debug.Log($"攻击速度: {attackSpeed.ToString("F2")}");
    Debug.Log($"暴击率: {(criticalRate * 100f).ToString("F1")}%");
}
```

## 总结

SimpleObscuredFloat 提供了对浮点数的有效保护，同时保持了良好的使用体验。通过位级联合体技术和精度处理，确保了浮点运算的正确性和安全性。在游戏开发中，它特别适合保护重要的数值参数，防止内存修改器的篡改。
