using System;
using UnityEngine;


/// <summary>
/// 简化版本的ObscuredLong，用于热更代码中的数值保护
/// </summary>
[Serializable]
public struct SimpleObscuredLong : IEquatable<SimpleObscuredLong>, IEquatable<long>, IComparable<SimpleObscuredLong>, IComparable<long>, IComparable
{
    [SerializeField] private long hiddenValue;
    [SerializeField] private long currentCryptoKey;

    private SimpleObscuredLong(long value)
    {
        currentCryptoKey = GenerateKey();
        hiddenValue = Encrypt(value, currentCryptoKey);
    }

    /// <summary>
    /// 加密数值
    /// </summary>
    public static long Encrypt(long value, long key)
    {
        unchecked
        {
            return (value ^ key) + key;
        }
    }

    /// <summary>
    /// 解密数值
    /// </summary>
    public static long Decrypt(long value, long key)
    {
        unchecked
        {
            return (value - key) ^ key;
        }
    }

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    public static long GenerateKey()
    {
        return System.DateTime.Now.Ticks;
    }

    /// <summary>
    /// 获取解密后的值
    /// </summary>
    public long GetDecrypted()
    {
        return InternalDecrypt();
    }

    /// <summary>
    /// 随机化密钥
    /// </summary>
    public void RandomizeCryptoKey()
    {
        var decrypted = InternalDecrypt();
        currentCryptoKey = GenerateKey();
        hiddenValue = Encrypt(decrypted, currentCryptoKey);
    }

    private long InternalDecrypt()
    {
        if (IsDefault()) 
            return 0;
        
        return Decrypt(hiddenValue, currentCryptoKey);
    }

    private bool IsDefault()
    {
        return hiddenValue == default && currentCryptoKey == default;
    }

    // 隐式转换操作符
    public static implicit operator SimpleObscuredLong(long value)
    {
        return new SimpleObscuredLong(value);
    }

    public static implicit operator long(SimpleObscuredLong value)
    {
        return value.InternalDecrypt();
    }

    // 算术操作符
    public static SimpleObscuredLong operator ++(SimpleObscuredLong input)
    {
        return input.InternalDecrypt() + 1;
    }

    public static SimpleObscuredLong operator --(SimpleObscuredLong input)
    {
        return input.InternalDecrypt() - 1;
    }

    public static SimpleObscuredLong operator +(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() + b.InternalDecrypt();
    }

    public static SimpleObscuredLong operator -(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() - b.InternalDecrypt();
    }

    public static SimpleObscuredLong operator *(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() * b.InternalDecrypt();
    }

    public static SimpleObscuredLong operator /(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() / b.InternalDecrypt();
    }

    public static SimpleObscuredLong operator %(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() % b.InternalDecrypt();
    }

    // 比较操作符
    public static bool operator ==(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() == b.InternalDecrypt();
    }

    public static bool operator !=(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() != b.InternalDecrypt();
    }

    public static bool operator >(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() > b.InternalDecrypt();
    }

    public static bool operator <(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() < b.InternalDecrypt();
    }

    public static bool operator >=(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() >= b.InternalDecrypt();
    }

    public static bool operator <=(SimpleObscuredLong a, SimpleObscuredLong b)
    {
        return a.InternalDecrypt() <= b.InternalDecrypt();
    }

    // 接口实现
    public bool Equals(SimpleObscuredLong other)
    {
        return InternalDecrypt() == other.InternalDecrypt();
    }

    public bool Equals(long other)
    {
        return InternalDecrypt() == other;
    }

    public override bool Equals(object obj)
    {
        if (obj is SimpleObscuredLong obscured)
            return Equals(obscured);
        if (obj is long longValue)
            return Equals(longValue);
        return false;
    }

    public override int GetHashCode()
    {
        return InternalDecrypt().GetHashCode();
    }

    public int CompareTo(SimpleObscuredLong other)
    {
        return InternalDecrypt().CompareTo(other.InternalDecrypt());
    }

    public int CompareTo(long other)
    {
        return InternalDecrypt().CompareTo(other);
    }

    public int CompareTo(object obj)
    {
        if (obj is SimpleObscuredLong obscured)
            return CompareTo(obscured);
        if (obj is long longValue)
            return CompareTo(longValue);
        throw new ArgumentException("Object must be of type SimpleObscuredLong or long");
    }

    public override string ToString()
    {
        return InternalDecrypt().ToString();
    }

    public string ToString(string format)
    {
        return InternalDecrypt().ToString(format);
    }

    public string ToString(IFormatProvider provider)
    {
        return InternalDecrypt().ToString(provider);
    }

    public string ToString(string format, IFormatProvider provider)
    {
        return InternalDecrypt().ToString(format, provider);
    }
}

