# 已修复的问题

## Unity Random 限制问题

### 问题描述
在Unity中，`UnityEngine.Random.Range` 不能在MonoBehaviour的构造函数或字段初始化器中调用，会抛出异常：
```
RandomRangeInt is not allowed to be called from a MonoBehaviour constructor (or instance field initializer), call it in Awake or Start instead.
```

### 解决方案
将密钥生成从 `UnityEngine.Random.Range` 改为使用 `System.Random`：

**修复前：**
```csharp
public static int GenerateKey()
{
    return UnityEngine.Random.Range(1000000000, int.MaxValue);
}
```

**修复后：**
```csharp
public static int GenerateKey()
{
    // 使用System.Random避免Unity的限制，结合多个随机源提高随机性
    var seed = System.Environment.TickCount ^ System.DateTime.Now.Millisecond ^ System.Threading.Thread.CurrentThread.ManagedThreadId;
    var random = new System.Random(seed);
    return random.Next(1000000000, int.MaxValue);
}
```

### 改进点
1. **避免Unity限制**：使用 `System.Random` 替代 `UnityEngine.Random`
2. **提高随机性**：组合多个随机源作为种子
   - `Environment.TickCount`：系统启动后的毫秒数
   - `DateTime.Now.Millisecond`：当前时间的毫秒部分
   - `Thread.CurrentThread.ManagedThreadId`：当前线程ID
3. **保持安全性**：密钥范围仍然是 10亿到 int.MaxValue

## 迁移状态

### 已完成
- ✅ 创建 SimpleObscuredInt 和 SimpleObscuredLong 类
- ✅ 修复 Unity Random 限制问题
- ✅ BattleUnit.cs 已成功迁移到新的类型
- ✅ 保持完整的API兼容性

### 验证方法
1. 编译项目无错误
2. 运行 SimpleObscuredTest 脚本验证功能
3. 检查 BattleUnit 中的数值操作正常

### 性能特点
- 比原版 ObscuredTypes 稍快（移除了额外验证）
- 比普通 int/long 慢 2-3 倍（加密解密开销）
- 适合用于重要数值，不适合频繁计算

## 使用建议

### 最佳实践
```csharp
// 推荐：缓存解密值进行计算
long currentHp = player.curHp; // 只解密一次
for (int i = 0; i < 1000; i++)
{
    if (currentHp > 0) // 使用缓存值
    {
        // 处理逻辑
    }
}

// 不推荐：频繁访问加密值
for (int i = 0; i < 1000; i++)
{
    if (player.curHp > 0) // 每次都要解密
    {
        // 处理逻辑
    }
}
```

### 定期随机化密钥
```csharp
// 在适当的时机随机化密钥以增强安全性
if (UnityEngine.Random.Range(0, 100) < 5) // 5% 概率
{
    playerScore.RandomizeCryptoKey();
    playerMoney.RandomizeCryptoKey();
}
```

## 总结

现在你可以在热更代码中安全使用数值保护功能，无需依赖 AntiCheatToolkit DLL。SimpleObscured 类提供了与原版相同的使用体验，同时解决了Unity的使用限制。
