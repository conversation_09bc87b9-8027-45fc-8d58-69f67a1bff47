using System;
using UnityEngine;


/// <summary>
/// 简化版本的ObscuredInt，用于热更代码中的数值保护
/// </summary>
[Serializable]
public struct SimpleObscuredInt : IEquatable<SimpleObscuredInt>, IEquatable<int>, IComparable<SimpleObscuredInt>, IComparable<int>, IComparable
{
    [SerializeField] private int hiddenValue;
    [SerializeField] private int currentCryptoKey;

    private SimpleObscuredInt(int value)
    {
        currentCryptoKey = GenerateKey();
        hiddenValue = Encrypt(value, currentCryptoKey);
    }

    /// <summary>
    /// 加密数值
    /// </summary>
    public static int Encrypt(int value, int key)
    {
        unchecked
        {
            return (value ^ key) + key;
        }
    }

    /// <summary>
    /// 解密数值
    /// </summary>
    public static int Decrypt(int value, int key)
    {
        unchecked
        {
            return (value - key) ^ key;
        }
    }

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    public static int GenerateKey()
    {
        return UnityEngine.Random.Range(1000000000, int.MaxValue);
    }

    /// <summary>
    /// 获取解密后的值
    /// </summary>
    public int GetDecrypted()
    {
        return InternalDecrypt();
    }

    /// <summary>
    /// 随机化密钥
    /// </summary>
    public void RandomizeCryptoKey()
    {
        var decrypted = InternalDecrypt();
        currentCryptoKey = GenerateKey();
        hiddenValue = Encrypt(decrypted, currentCryptoKey);
    }

    private int InternalDecrypt()
    {
        if (IsDefault()) 
            return 0;
        
        return Decrypt(hiddenValue, currentCryptoKey);
    }

    private bool IsDefault()
    {
        return hiddenValue == default && currentCryptoKey == default;
    }

    // 隐式转换操作符
    public static implicit operator SimpleObscuredInt(int value)
    {
        return new SimpleObscuredInt(value);
    }

    public static implicit operator int(SimpleObscuredInt value)
    {
        return value.InternalDecrypt();
    }

    // 算术操作符
    public static SimpleObscuredInt operator ++(SimpleObscuredInt input)
    {
        return input.InternalDecrypt() + 1;
    }

    public static SimpleObscuredInt operator --(SimpleObscuredInt input)
    {
        return input.InternalDecrypt() - 1;
    }

    public static SimpleObscuredInt operator +(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() + b.InternalDecrypt();
    }

    public static SimpleObscuredInt operator -(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() - b.InternalDecrypt();
    }

    public static SimpleObscuredInt operator *(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() * b.InternalDecrypt();
    }

    public static SimpleObscuredInt operator /(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() / b.InternalDecrypt();
    }

    public static SimpleObscuredInt operator %(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() % b.InternalDecrypt();
    }

    // 比较操作符
    public static bool operator ==(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() == b.InternalDecrypt();
    }

    public static bool operator !=(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() != b.InternalDecrypt();
    }

    public static bool operator >(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() > b.InternalDecrypt();
    }

    public static bool operator <(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() < b.InternalDecrypt();
    }

    public static bool operator >=(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() >= b.InternalDecrypt();
    }

    public static bool operator <=(SimpleObscuredInt a, SimpleObscuredInt b)
    {
        return a.InternalDecrypt() <= b.InternalDecrypt();
    }

    // 接口实现
    public bool Equals(SimpleObscuredInt other)
    {
        return InternalDecrypt() == other.InternalDecrypt();
    }

    public bool Equals(int other)
    {
        return InternalDecrypt() == other;
    }

    public override bool Equals(object obj)
    {
        if (obj is SimpleObscuredInt obscured)
            return Equals(obscured);
        if (obj is int intValue)
            return Equals(intValue);
        return false;
    }

    public override int GetHashCode()
    {
        return InternalDecrypt().GetHashCode();
    }

    public int CompareTo(SimpleObscuredInt other)
    {
        return InternalDecrypt().CompareTo(other.InternalDecrypt());
    }

    public int CompareTo(int other)
    {
        return InternalDecrypt().CompareTo(other);
    }

    public int CompareTo(object obj)
    {
        if (obj is SimpleObscuredInt obscured)
            return CompareTo(obscured);
        if (obj is int intValue)
            return CompareTo(intValue);
        throw new ArgumentException("Object must be of type SimpleObscuredInt or int");
    }

    public override string ToString()
    {
        return InternalDecrypt().ToString();
    }

    public string ToString(string format)
    {
        return InternalDecrypt().ToString(format);
    }

    public string ToString(IFormatProvider provider)
    {
        return InternalDecrypt().ToString(provider);
    }

    public string ToString(string format, IFormatProvider provider)
    {
        return InternalDecrypt().ToString(format, provider);
    }
}

