using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Triggers;
using DG.Tweening;
using FairyGUI;
using Proto.LogicData;
using Proto.Types;
using Spine;
using Spine.Unity;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions;


public class Monster : BattleUnit, IBattleObject
{
    private static int ShaderHurtId = Shader.PropertyToID("HurtIntensity");

    public bool isLastBoss { get { return _gateVo.info.waveType == GateWaveType.LastBoss; } }

    protected InfoMonster _info;
    protected GateMonsterCreateVo _gateVo;
    protected InfoGateTemplate _gateInfo;

    private bool isAddImpulseForce;
    private SpriteRenderer _spriteRenderer;
    private Rigidbody2D _rigidbody2d;
    private Collider2D _collider;
    private Animator _animator;
    private MaterialPropertyBlock _materialBlock;

    private bool _isUseSpine;
    private SkeletonAnimation _animatorSpine;
    private float _initAniTimeScale = 1f;

    private MeshRenderer _meshRenderer;

    private Vector3 _moveTargetPos;
    private Vector2 _moveDir;
    private bool _isArriveTargetPos;
    private float _preHitTime = 0f;

    protected bool _isStopStatus = false;

    protected bool _canMove = true;
    protected Vector3 _initScale;
    protected Transform _firePos;
    private Vector3 _fireLocalPos;

    protected float _initMass = 1f;

    protected Dictionary<string, string> _preBuffAniNames = new Dictionary<string, string>();
    private Transform myTransform;
    private ProgressBar hpBar;

    private List<GateBossHpLevelInfo> hpLevelInfos;
    private int dieAndStrongerPer = 1;
    protected override void OnAwake()
    {
        base.OnAwake();
        tag = TagEnum.Enemy.ToString();
        myTransform = transform;
        _animatorSpine = transform.GetComponentInChildren<SkeletonAnimation>();
        if (_animatorSpine != null)
        {
            //Spine
            _materialBlock = new MaterialPropertyBlock();

            Assert.IsNotNull(_animatorSpine);
            _isUseSpine = true;
            _initAniTimeScale = _animatorSpine.timeScale;
            _meshRenderer = GetComponentInChildren<MeshRenderer>();
            if (_meshRenderer != null)
            {
                _meshRenderer.GetPropertyBlock(_materialBlock);
            }
        }
        else
        {
            //序列帧
            _spriteRenderer = transform.GetComponentInChildren<SpriteRenderer>();
            _animator = GetComponentInChildren<Animator>();
            Assert.IsNotNull(_animator);
        }
        var prgMonster = transform.Find("prgMonster");
        if (prgMonster != null)
        {
            prgMonster.TryGetComponent(out hpBar);
        }

        _rigidbody2d = GetComponent<Rigidbody2D>();
        if (_rigidbody2d != null) {
            _initMass = _rigidbody2d.mass;
        }
        _collider = GetComponent<Collider2D>();
        _firePos = this.transform.Find("firePos");
        if (_firePos != null)
        {
            _fireLocalPos = _firePos.transform.localPosition;
        }
        _initScale = this.transform.localScale;
    }

    internal void SetData(InfoMonster info, GateMonsterCreateVo gateVo, List<GateBossHpLevelInfo> hpLevelInfos = null, long maxHp = 0)
    {
        isDie = false;
        isAddImpulseForce = false;
        this._canMove = true;
        this._info = info;
        this._gateVo = gateVo;
        this._gateInfo = gateVo.info;
        this._isArriveTargetPos = false;
        this.hpLevelInfos = hpLevelInfos;

        if (maxHp > 0 && IsBoss())
        {
            curHp = this.maxHp = maxHp;
        }
        else
        {
            var hpAddPercent = BuffHelper.GetBattleEntryPercent(BuffEffectType.AddMonsterHpPercent, this.GetGateEntryEffectType());
            curHp = this.maxHp = (long)(this._gateInfo.hp * this._gateVo.monsterHpPercent * (1.0f + hpAddPercent));
        }

        if (hpBar != null)
        {
            hpBar.SetProgress(curHp, GetMaxHp());
            hpBar.gameObject.SetActive(true);
        }
        this.moveSpeed = _gateInfo.moveSpeed;

        //移动速度不为0的需要走到目的地才能开火，为0默认可开火
        enbaleFire = this.moveSpeed == 0;

        if (_collider != null)
            _collider.enabled = true;

        FlashTo(0);
        if (_spriteRenderer != null)
        {
            // var material = _spriteRenderer.material;
            // if (_spriteRenderer.material.HasFloat("_Strongth"))
            // {
            //     _spriteRenderer.material.SetFloat("_Strongth", 1);
            // }
            // if (_spriteRenderer.material.HasFloat("_HurtColor"))
            // {
            //     _spriteRenderer.material.SetFloat("_HurtColor", 0);
            // }
        }

        if (_animator != null) {
            _animator.enabled = true;
        }
        if (_animatorSpine != null) {
            _animatorSpine.timeScale = this._initAniTimeScale;
        }

        if (_rigidbody2d != null) {
            _rigidbody2d.mass = _initMass;
        }
        PlayAction(AniNames.Move, true);
    }

    /// <summary>
    /// 获取移动到目标点的区域索引
    /// </summary>
    /// <returns></returns>
    public int GetMoveTargetAreaIndex()
    {
        if (_info == null)
            return -1;
        return _info.moveTargetAreaIndex;
    }


    /// <summary>
    /// 播放出现效果
    /// </summary>
    public void PlayAppearEffect()
    {
        this.transform.localScale = Vector3.zero;
        this.transform.DOScale(_initScale, 0.1f).SetLink(this.gameObject);
    }

    public void Init()
    {
        var weaponIds = _info.weaponIds;
        for (int i = 0; i < weaponIds.Length; i++)
        {
            EquipWeapon(weaponIds[i]);
        }
    }

    public void RemoveWeapons()
    {
        var weaponIds = _info.weaponIds;
        for (int i = 0; i < weaponIds.Length; i++)
        {
            RemoveWeapon(weaponIds[i]);
        }
    }

    /// <summary>
    /// 攻击CD
    /// </summary>
    /// <returns></returns>
    public float GetAttackCd()
    {
        if (_info == null)
            return 1f;
        var addPercent = 1f + BuffHelper.GetTotalPercent(this, BuffEffectType.AddMonsterAttackSpeedPercent, this.GetGateEntryEffectType());
        if (addPercent < 0f)
        {
            addPercent = 0.01f;
        }
        var cd = _info.attackCd / addPercent;
        return cd;
    }

    public int GetMonsterId()
    {
        if (_info == null)
            return 0;
        return _info.id;
    }


    /// <summary>
    /// 怪物类型
    /// </summary>
    /// <returns></returns>
    public int GetMonsterType()
    {
        if (_info == null)
            return 0;
        return _info.type;
    }

    /// <summary>
    /// 关卡词条
    /// </summary>
    /// <returns></returns>
    public int GetGateEntryEffectType()
    {
        if (_info == null)
            return 0;
        return _info.gateEntryEffectType;
    }

    /// <summary>
    /// 怪物名
    /// </summary>
    /// <returns></returns>
    public string GetMonsterName()
    {
        if (null == _gateVo)
            return "";
        return _gateVo.monsterInfo.name;
    }

    /// <summary>
    /// 图标
    /// </summary>
    /// <returns></returns>
    public string GetIcon()
    {
        if (null == _info)
            return "";

        return _info.icon;
    }

    /// <summary>
    /// 最大血量
    /// </summary>
    /// <returns></returns>
    override public long GetMaxHp()
    {
        return maxHp;
    }

    /// <summary>
    /// 是否是BOSS
    /// </summary>
    /// <returns></returns>
    public bool IsBoss()
    {
        if (null == _info)
            return false;
        return ((MonsterType)_info.type == MonsterType.Boss);
    }

    /// <summary>
    /// 是否是小怪
    /// </summary>
    /// <returns></returns>
    public bool IsNormal()
    {
        if (null == _info)
            return false;
        return ((MonsterType)_info.type == MonsterType.Normal);
    }

    /// <summary>
    /// 掉落经验
    /// </summary>
    /// <returns></returns>
    public int GetExp()
    {
        if (null == _gateInfo)
            return 0;
        return (int)_gateInfo.exp;
    }

    public void ReviveAndStrong()
    {
        isDie = false;
        dieAndStrongerPer += 1;
        maxHp = curHp = 2 * maxHp;
    }

    /// <summary>
    /// 碰撞&近战暴击率
    /// </summary>
    /// <returns></returns>
    public float GetCollisionCriticalRate()
    {
        if (null == _info)
            return 0f;

        var rate = _info.critical + BuffHelper.GetBattleEntryPercent(BuffEffectType.AddMonsterCollisionCriticalPercent, GetGateEntryEffectType());
        return rate;
    }

    /// <summary>
    /// 闪避率
    /// </summary>
    /// <returns></returns>
    public float GetDodgeRate()
    {
        if (null == _info)
            return 0f;

        var rate = _info.dodge + BuffHelper.GetBattleEntryPercent(BuffEffectType.AddMonsterdodgePercent, this.GetGateEntryEffectType());
        return rate;
    }

    /// <summary>
    /// BUFF更新
    /// </summary>
    /// <returns></returns>
    override protected void OnUpdateBuff(float deltaTime)
    {
        base.OnUpdateBuff(deltaTime);
        RefresBuffStatus();
    }

    /// <summary>
    /// 刷新BUFF状态
    /// </summary>
    protected void RefresBuffStatus()
    {
        //处理BUFF效果
        //麻痹，眩晕，冰冻等静止类BUFF
        CheckIsStopStatus();

        //移动速度
        float speedChangePercent = 1f + GetMoveSpeedAddPercent();
        this.moveSpeed = _gateInfo.moveSpeed * speedChangePercent;
        if (this.moveSpeed < 0f) {
            this.moveSpeed = 0f;
        }

        if (this._isStopStatus || speedChangePercent < 1f) {
            ChangeToBlockStatus(true);
        } else {
            ChangeToBlockStatus(false);
        }

        //攻击速度

        //处理BUFF特效
        CheckBuffAniList();
    }

    protected void ChangeToBlockStatus(bool bBlock)
    {
        if (this.GetMonsterType() == (int)MonsterType.Normal) {
            //小怪需要设置质量为一个较大值，让小怪推不动
            if (this._rigidbody2d != null) {
                if (bBlock) {
                    this._rigidbody2d.mass = 99f;
                    _rigidbody2d.velocity = Vector2.zero;
                } else {
                    this._rigidbody2d.mass = this._initMass;
                }
            }
        }


        if (this._isStopStatus) {
            //停止动作
            StopAction(true);
        } else {
            //恢复动作
            StopAction(false);
        }
    }

    /// <summary>
    /// 检查是否是停止状态
    /// </summary>
    /// <returns></returns>
    public bool CheckIsStopStatus()
    {
        //静止
        _isStopStatus = _buffManager.HasEffectBuff((int)BuffEffectType.StopStatus);
        if (_isStopStatus) {
            return _isStopStatus;
        }

        //麻痹
        _isStopStatus = _buffManager.HasEffectBuff((int)BuffEffectType.PalsyStatus);
        if (_isStopStatus) {
            return _isStopStatus;
        }

        //冰冻
        _isStopStatus = _buffManager.HasEffectBuff((int)BuffEffectType.FrozenStatus);
        if (_isStopStatus) {
            return _isStopStatus;
        }

        //眩晕
        _isStopStatus = _buffManager.HasEffectBuff((int)BuffEffectType.VertigoStatus);
        if (_isStopStatus) {
            return _isStopStatus;
        }

        //眩晕
        _isStopStatus = _buffManager.HasEffectBuff((int)BuffEffectType.SpecialVertigoStatus);
        if (_isStopStatus) {
            return _isStopStatus;
        }

        _isStopStatus = false;
        return _isStopStatus;
    }

    /// <summary>
    /// 移动速度百分比
    /// </summary>
    /// <returns></returns>
    public float GetMoveSpeedAddPercent()
    {
        float percent = 0f;
        //状态影响
        var buffList = _buffManager.GetBuffListByEffect((int)BuffEffectType.MoveSpeedStatus);
        if (buffList != null)
        {
            foreach (var buff in buffList)
            {
                percent += buff.floatValue;
            }
        }

        //关卡词条影响
        percent += BuffHelper.GetBattleEntryPercent(BuffEffectType.AddMonsterMoveSpeedPercent, this.GetGateEntryEffectType());

        return percent;
    }

    /// <summary>
    /// 检查BUFF动画
    /// </summary>
    protected void CheckBuffAniList()
    {
        var buffNames = _buffManager.GetBuffAniNameList();

        //移除原有时效的BUFF特效
        foreach (var preBuffName in _preBuffAniNames.Keys)
        {
            if (!buffNames.ContainsKey(preBuffName))
            {
                this.RemoveEffectAni(preBuffName);
            }
        }

        //更新BUFF
        _preBuffAniNames.Clear();
        foreach (var buffName in buffNames.Values)
        {
            var effect = this.AddEffectAni(buffName);
            if (effect != null && effect.IsForEever())
            {
                _preBuffAniNames[buffName] = buffName;
            }
        }
    }

    protected override Vector4? GetBuffEffectOffsetAndScale(string effectName)
    {
        if (_info == null)
            return null;

        return _info.GetBuffOffsetAndScale(effectName);
    }


    /// <summary>
    /// 计算碰撞&近战暴击倍率
    /// </summary>
    /// <returns></returns>
    public float ComputeCollisionDamageMultiplier()
    {
        var rate = GetCollisionCriticalRate();
        if (UnityEngine.Random.value <= rate)
        {
            return 2f;
        }
        return 1f;
    }

    /// <summary>
    /// 碰撞产生的伤害
    /// </summary>
    /// <returns></returns>
    public int GetCollisonDamage()
    {
        var demage = GetAttackValue();
        return (int)demage;
    }

    /// <summary>
    /// 加入BUFF的时候免疫判断
    /// </summary>
    /// <param name="infoBuff"></param>
    /// <returns></returns>
    override public bool IsIgnoreBuff(InfoBuff infoBuff)
    {
        bool ignore = false;
        //免疫判断
        switch ((BuffEffectType)infoBuff.effect)
        {
            case BuffEffectType.StopStatus:
                ignore = IsStopStatusIgnore();
                break;
            case BuffEffectType.PalsyStatus:
                ignore = IsPalsyIgnore();
                break;
            case BuffEffectType.FrozenStatus:
                ignore = IsFrozenIgnore();
                break;
            case BuffEffectType.VertigoStatus:
                ignore = IsVertigoIgnore();
                break;
            case BuffEffectType.BuffReduceHpOnBulletHit:
                ignore = IsReduceHpIgnore(BuffEffectType.MonsterReduceHpOnBulletHitIgnore);
                break;
            case BuffEffectType.BuffReduceHpOnBulletHit1:
                ignore = IsReduceHpIgnore(BuffEffectType.MonsterReduceHpOnBulletHitIgnore1);
                break;
            case BuffEffectType.BuffReduceHpOnBulletHit2:
                ignore = IsReduceHpIgnore(BuffEffectType.MonsterReduceHpOnBulletHitIgnore2);
                break;
            case BuffEffectType.BuffReduceHpOnBulletHit3:
                ignore = IsReduceHpIgnore(BuffEffectType.MonsterReduceHpOnBulletHitIgnore3);
                break;
            case BuffEffectType.MoveSpeedStatus:
                ignore = IsMoveSpeedSubIgnore();
                break;
        }


        return ignore;
    }

    /// <summary>
    /// 攻击伤害
    /// </summary>
    /// <returns></returns>
    override public float GetAttackValue()
    {
        int damage = _gateInfo != null ? (int)(_gateInfo.attackValue * _gateVo.monsterAtkPercent * dieAndStrongerPer) : 0;
        //关卡词条
        float addPercent = BuffHelper.GetBattleEntryPercent(BuffEffectType.AddMonsterAttackPercent, this.GetGateEntryEffectType());
        var bossAddPercent = 0f;
        if (IsBoss() && hpLevelInfos != null && hpLevelInfos.Count > 0)
        {
            var bossDamage = BattleSkillPool.GetTotalBossDamage();

            var curPercent = (((float)(bossDamage) / (float)GetMaxHp()) * 10000f);
            bossAddPercent = hpLevelInfos[0].AtkAddPercent * 0.0001f;
            foreach (var hpInfo in hpLevelInfos)
            {
                if (curPercent <= hpInfo.HpPercent)
                {
                    bossAddPercent = hpInfo.AtkAddPercent * 0.0001f;
                    break;
                }
            }
        }
        damage = (int)(damage * (1f + addPercent + bossAddPercent));
        return damage;
    }

    /// <summary>
    /// 减速免疫
    /// </summary>
    /// <returns></returns>
    public bool IsMoveSpeedSubIgnore()
    {
        return BuffHelper.HasBattleEntryEffect(BuffEffectType.MonsterMoveSpeedSubIgnore, this.GetGateEntryEffectType());
    }

    /// <summary>
    /// 静止免疫
    /// </summary>
    /// <returns></returns>
    public bool IsStopStatusIgnore()
    {
        return BuffHelper.HasBattleEntryEffect(BuffEffectType.MonsterStopStausIgnore, this.GetGateEntryEffectType());
    }

    /// <summary>
    /// 是否麻痹免疫
    /// </summary>
    /// <returns></returns>
    public bool IsPalsyIgnore()
    {
        if (BuffHelper.HasBattleEntryEffect(BuffEffectType.MonsterPalsyIgnore, this.GetGateEntryEffectType())) {
            return true;
        }
        return false;
    }

    /// <summary>
    /// 冰冻免疫
    /// </summary>
    /// <returns></returns>
    public bool IsFrozenIgnore()
    {
        if (BuffHelper.HasBattleEntryEffect(BuffEffectType.MonsterFrozenIgnore, this.GetGateEntryEffectType())) {
            return true;
        }
        return false;
    }

    /// <summary>
    /// 眩晕免疫
    /// </summary>
    /// <returns></returns>
    public bool IsVertigoIgnore()
    {
        if (BuffHelper.HasBattleEntryEffect(BuffEffectType.MonsterVertigoIgnore, this.GetGateEntryEffectType())) {
            return true;
        }
        return false;
    }

    /// <summary>
    /// 持续减伤免疫
    /// </summary>
    /// <returns></returns>
    public bool IsReduceHpIgnore(BuffEffectType type)
    {
        if (BuffHelper.HasBattleEntryEffect(type, this.GetGateEntryEffectType())) {
            return true;
        }
        return false;
    }

    /// <summary>
    /// 碰撞后是否死亡
    /// </summary>
    /// <returns></returns>
    public bool CollisionNeedDie()
    {
        if (_gateVo == null)
            return true;
        return _gateVo.info.collisionDie != 0;
    }

    /// <summary>
    /// 设置移动目标点
    /// </summary>
    /// <param name="targetPos"></param>
    public void SetMoveTargetPos(Vector3 targetPos)
    {
        _moveTargetPos = targetPos;
        _moveDir = _moveTargetPos - transform.position;
        _moveDir.Normalize();
        Flip(_moveDir);
    }

    internal void EnableMovable(bool enable)
    {
        _canMove = enable;
    }

    /// <summary>
    /// 移动
    /// </summary>
    internal void Move()
    {
        if (ReferenceEquals(_rigidbody2d, null))
            return;

        if (isAddImpulseForce)
            return;

        if (_isStopStatus || !_canMove)
        {
            _rigidbody2d.velocity = Vector2.zero;
            return;
        }
        if (isDie)
        {
            _rigidbody2d.velocity = Vector2.zero;
            return;
        }

        if (moveSpeed == 0)
            return;

        if (Time.frameCount % 4 != 0)
            return;

        if (_isArriveTargetPos)
            return;

        var monsterPos = myTransform.position;
        if (monsterPos.y < _moveTargetPos.y)
        {
            _isArriveTargetPos = true;
            enbaleFire = true;
            myTransform.position = _moveTargetPos;
            _rigidbody2d.velocity = Vector2.zero;
            moveSpeed = 0;
            OnMoveComplete();
            return;
        }

        _rigidbody2d.velocity = _moveDir * moveSpeed;
    }

    protected void OnMoveComplete()
    {
        if (GetMonsterType() == MonsterType.Normal.GetHashCode())//普通怪只有移动动作
            return;

        PlayAction(AniNames.Idle, true);
    }

    override public void Flip(Vector3 moveDir)
    {
        var needFlip = moveDir.x < 0;
        if (_isUseSpine)
        {
            var targetFlip = needFlip ? -1 : 1;
            if (targetFlip != _animatorSpine.skeleton.ScaleX)
            {
                if (_firePos != null)
                {
                    var localPos = needFlip ? new Vector3(-_fireLocalPos.x, _fireLocalPos.y, _fireLocalPos.z) : _fireLocalPos;
                    _firePos.transform.localPosition = localPos;
                }
                _animatorSpine.skeleton.ScaleX = targetFlip;
            }
        }
        else
        {
            if (needFlip != _spriteRenderer.flipX)
            {
                _spriteRenderer.flipX = needFlip;
            }
        }
    }

    override public Transform GetWeaponNode()
    {
        if (_firePos != null)
        {
            return _firePos;
        }
        return transform;
    }

    /// <summary>
    /// 改变血量
    /// </summary>
    /// <param name="value"></param>
    /// <param name="displayValue"></param>
    override public void ChangeHp(int value, bool displayValue = true, int relatedSkillLink = 0)
    {
        if (value > 0)
        {
            AddHp(value, displayValue);
        }
        else
        {
            if (!isDie)
            {
                BattleSkillPool.AddSkillDebuffDamage(relatedSkillLink, -value, IsBoss());
            }
            Hurt(-value);
        }
    }

    /// <summary>
    /// 直接伤害，不再计算减伤
    /// </summary>
    /// <param name="damage"></param>
    /// <param name="damageMultiplier"></param>
    /// <param name="flyHurtNum"></param>
    /// <param name="hurtEffect"></param>
    public void Hurt(int damage, float damageMultiplier = 1f, bool flyHurtNum = true, string hurtEffect = null)
    {
        if (isDie)
            return;
        if (flyHurtNum)
        {
            FlyEffect.HurtNum(myTransform.position, damage, GetDamageColor(damageMultiplier), GetDamageScale(damageMultiplier));
        }

        curHp -= damage;
        if (curHp <= 0)
        {
            curHp = 0;
            if (this.IsNormal()) {
                int addExp = GetExp();
                var curHandler = (GameMain.curHandler as BattleHandler);
                if (curHandler != null)
                {
                    if (curHandler.GetAddExp() > 0)
                    {
                        addExp = Mathf.FloorToInt(addExp * curHandler.GetAddExp());
                    }
                }
                BattleUnitManager.Inst.player.CollectExp(addExp);

                BattleUnitManager.Inst.player.killCount++;
            }
            PlayEffect(hurtEffect);

            if (IsBoss()) {
                BattleUnitManager.Inst.player.killBossCount++;
                var curHandler = (GameMain.curHandler as BattleHandler);
                if (curHandler != null)
                {
                    if (curHandler.IsBattlePeakType())
                    {
                        ReviveAndStrong();

                        TipMgr.ShowTip(LangUtil.GetText("txtBossReviveAndStronger"));
                        return;
                    }
                }
            }
            Die();
        }
        else
        {
            PlayEffect(hurtEffect);
            PlayHurtFlash();
        }

        if (hpBar != null)
        {
            hpBar.SetProgress(curHp, GetMaxHp());
            if (curHp == 0)
            {
                hpBar.gameObject.SetActive(false);
            }
        }
    }

    /// <summary>
    /// 子弹命中
    /// </summary>
    /// <param name="bullet"></param>
    public override void OnBulletHit(BulletBase bullet)
    {
        var missRate = GetDodgeRate();
        if (missRate > 0f && UnityEngine.Random.value < missRate)
        {
            //触发闪避，播放闪避效果
            FlyEffect.Miss(this.myTransform.position);
            return;
        }

        var weaponOwner = bullet.GetWeaponOwner();
        if (weaponOwner == null)
            return;

        float damage = bullet.GetAttackValue();//初始攻击
        float damageMultiplier = bullet.GetDamageMultiplier();//初始加成倍率

        //累计攻击次数伤害加成
        var stackDamagePercent = GetStackDamagePercent(bullet);
        damageMultiplier += stackDamagePercent;

        damage = (long)(damage * damageMultiplier);//加成后伤害

        //以下使用加成后的damage计算
        //攻击属性加成或者减免
        var atkPercent = BuffHelper.GetBattleTotalPercentLimitByParam0(BuffEffectType.MonsterHarmPercentByAtkType,
                this.GetGateEntryEffectType(), bullet.GetAtkType());

        damage *= 1f + atkPercent + BattleUnitManager.Inst.player.atkAddPercent + stackDamagePercent;

        float multiplier = bullet.ComputeCriticalDamageMultiplier();
        damage *= multiplier;
        this.Knockback(bullet.GetFirePos(), bullet.knockbackStrength, 0.15f);
        this.OnHurt((int)damage, multiplier, bullet.GetHitEffect());

        if (!this.isDie)
        {
            //附加BUFF效果
            if (CheckHitCreateBuff(bullet))
            {
                RefresBuffStatus();
            }
        }

        var skillLink = bullet.GetRelatedSkillLink();
        if (skillLink > 0)
        {
            BattleSkillPool.AddSkillDamage(skillLink, (long)damage, GetMonsterType() == (int)MonsterType.Boss);
        }
    }

    /// <summary>
    /// 叠加伤害（根据击中次数）
    /// </summary>
    /// <param name="bullet"></param>
    /// <returns></returns>
    public float GetStackDamagePercent(BulletBase bullet)
    {
        float stackPercent = BuffHelper.GetBulletStackDamagePercent(bullet.GetBulletVo());
        if (stackPercent == 0)
            return 0;

        int stackCount = 0;
        int maxStackCount = 0;
        var buffList = _buffManager.GetBuffListByEffect((int)BuffEffectType.AddDamageLevelByHitCount);
        if (buffList != null)
        {
            foreach (var buff in buffList)
            {
                if (buff.infoBuff.effectGroup > 0 && buff.infoBuff.effectGroup != bullet.GetBulletEffectGroup())
                {
                    //限定了效果组，并且效果组不匹配，不生效
                    continue;
                }
                maxStackCount = buff.infoBuff.param0;
                stackCount += buff.value;
            }
        }
        stackCount = Mathf.Min(stackCount, maxStackCount);
        return stackCount * stackPercent;
    }

    /// <summary>
    /// 命中产生BUFF
    /// </summary>
    protected bool CheckHitCreateBuff(BulletBase bullet)
    {
        var owner = bullet.GetWeaponOwner();
        if (null == owner) {
            return false;
        }

        //武器命中后产生效果的概率
        var buffList = owner.GetBuffListByEffect(BuffEffectType.BulletHitCreateBuffRate);
        if (null == buffList) {
            return false;
        }

        int creteBuffCount = 0;

        var effectGroup = bullet.GetBulletEffectGroup();
        var layerLevel = bullet.GetBulletLayerLevel();
        int owerClasses = owner.GetClasses();

        //职业加成概率
        float addRatePercent = owner.GetTotalPercentLimitByParam0(BuffEffectType.HeroClassHitCreateBuffAddRate,
            owerClasses, layerLevel);

        float buffAddTimePercent = bullet.GetCreateBuffTimeAddPercent();

        foreach (var buff in buffList) {
            if (buff.infoBuff.effectGroup > 0 && buff.infoBuff.effectGroup != effectGroup) {
                //限定了效果组，并且效果组不匹配，不生效
                continue;
            }

            var newBuffId = buff.infoBuff.value;

            //部分BUFF的产生概率提升    
            addRatePercent += GetCreateBuffAddRatePercent(newBuffId);
            float createRate = buff.floatValue + addRatePercent;
            if (UnityEngine.Random.value <= createRate) {
                //部分BUFF的产生时长提升
                buffAddTimePercent += GetCreateBuffAddTimePercent(newBuffId);
                var newBuff = this.AddBuff(newBuffId, buffAddTimePercent);
                if (newBuff != null) {
                    newBuff.externParam = (float)bullet.GetDemage();
                    newBuff.relatedSkillLink = bullet.GetRelatedSkillLink();
                    ++creteBuffCount;
                }
            }
        }

        return creteBuffCount > 0;
    }

    /// <summary>
    /// 创建指定ID BUFF的概率
    /// </summary>
    /// <param name="newBuffId"></param>
    /// <returns></returns>
    public float GetCreateBuffAddRatePercent(int newBuffId)
    {
        var infoBuff = ConfigBuff.GetData(newBuffId);
        if (null == infoBuff)
            return 0f;
        return BuffHelper.GetBattleTotalPercentLimitByParam0(BuffEffectType.MonsterOnHitCreateBuffAddRate,
                this.GetGateEntryEffectType(), infoBuff.effect);
    }

    /// <summary>
    /// 创建指定ID 时长提升
    /// </summary>
    /// <param name="newBuffId"></param>
    /// <returns></returns>
    public float GetCreateBuffAddTimePercent(int newBuffId)
    {
        var infoBuff = ConfigBuff.GetData(newBuffId);
        if (null == infoBuff)
            return 0f;
        return BuffHelper.GetBattleTotalPercentLimitByParam0(BuffEffectType.MonsterOnHitCreateBuffTimeAddPercent,
                this.GetGateEntryEffectType(), infoBuff.effect);
    }


    public override void AddHp(int value, bool displayAddValue = true)
    {
        if (value <= 0)
            return;

        var deltaValue = value;
        long curMaxHp = GetMaxHp();
        if (curHp + value > curMaxHp)
        {
            deltaValue = (int)(curMaxHp - curHp);
        }

        curHp += deltaValue;

        if (displayAddValue && value > 0)
        {
            FlyEffect.HurtNum(transform.position, Mathf.FloorToInt(value), Color.green, 1);
        }
    }

    /// <summary>
    /// 需要计算减伤
    /// </summary>
    /// <param name="damage"></param>
    /// <param name="damageMultiplier"></param>
    /// <param name="hitEffect"></param>
    public override void OnHurt(int damage, float damageMultiplier = 1f, string hitEffect = null)
    {
        if (isDie)
            return;

        //减伤百分比
        var reducePercent = BuffHelper.GetBattleEntryPercent(BuffEffectType.ReduceMonsterHarmPercent, this.GetGateEntryEffectType());
        reducePercent += this._gateVo.monsterReduceHarmPercent;

        if (reducePercent > 0.75f) {
            Debug.AssertFormat(false, "reducePercent error {0}", reducePercent);
            reducePercent = 0.75f;
        }
        damage = (int)(damage * (1.0f - reducePercent));

        // if (damage <= 0)
        //     damage = 1;
        if (damage <= 0) {
            return;
        }
        Hurt(damage, damageMultiplier, true, hitEffect);
    }


    public override void Knockback(Vector3 attackerPos, float strength, float delay)
    {
        if (_info == null || _info.ignoreKnockback || strength <= 0)
            return;
        if (_rigidbody2d == null)
            return;

        var backDir = (transform.position - attackerPos).normalized;
        AddImpulseForce(backDir, strength * _rigidbody2d.mass, delay);
    }

    public void ResetVelocity()
    {
        if (_rigidbody2d == null)
            return;
        _rigidbody2d.velocity = Vector2.zero;
    }

    async public void AddImpulseForce(Vector3 dirNormal, float strength, float delay)
    {
        if (_rigidbody2d == null)
            return;

        ResetVelocity();
        isAddImpulseForce = true;
        _rigidbody2d.AddForce(dirNormal * strength, ForceMode2D.Impulse);
        await UniTask.Delay((int)(delay * 1000));
        isAddImpulseForce = false;
        if (this == null)
            return;
        ResetVelocity();
    }

    private void PlayEffect(string hitEffect)
    {
        if (!string.IsNullOrEmpty(hitEffect))
        {
            var pos = myTransform.position;

            var effectOffsetScale = GetBuffEffectOffsetAndScale(hitEffect);
            if (effectOffsetScale.HasValue)
            {
                pos.x += ((Vector4)effectOffsetScale).x;
                pos.y += ((Vector4)effectOffsetScale).y;
            }

            var effScale = effectOffsetScale.HasValue ? new Vector2(((Vector4)effectOffsetScale).z, ((Vector4)effectOffsetScale).w) : Vector2.one;
            FlyEffect.HurtEffect(hitEffect, pos, effScale);

            // var effectGo = EffectFactory.Create(hitEffect, pos);
            // if (effectGo != null && effectOffsetScale.HasValue)
            // {
            //     effectGo.transform.localScale = new Vector2(((Vector4)effectOffsetScale).z, ((Vector4)effectOffsetScale).w);
            // }
        }

    }

    private void PlayHurtFlash()
    {
        if (GetCurFlashValue() == 0)
        {
            FlashTo(1);
            DOTween.To(GetCurFlashValue, (x) => { FlashTo(x); }, 0, 0.2f).SetLink(this.gameObject);
        }
    }

    private static Color orange = new Color(1, 0.25f, 0);
    private static Color purple = new Color(0.88f, 0.11f, 1);
    private Color GetDamageColor(float damageMultiplier)
    {
        if (damageMultiplier < 1.5f)
        {
            return Color.white;
        }
        else if (damageMultiplier < 3)
        {
            return Color.yellow;
        }
        else if (damageMultiplier == 100)//反伤的颜色
        {
            return purple;
        }
        else
        {
            return orange;
        }
    }

    private float GetDamageScale(float damageMultiplier)
    {
        if (damageMultiplier < 1.5f)
        {
            return 1f;
        }
        else if (damageMultiplier < 3)
        {
            return 1.5f;
        }
        else
        {
            return 1.5f;
        }
    }

    private float GetCurFlashValue()
    {
        if (_isUseSpine)
        {
            return _materialBlock.GetFloat(ShaderHurtId);
        }
        else
        {
            return 1 - _spriteRenderer.color.a;
        }
    }

    private void FlashTo(float value)
    {
        if (_isUseSpine)
        {
            _materialBlock.SetFloat(ShaderHurtId, value);
            _meshRenderer.SetPropertyBlock(_materialBlock);
        }
        else
        {
            //使用color属性设置受击效果，防止批次被打断
            var color = _spriteRenderer.color;
            color.a = 1 - value;//a=0才是最白的时候
            _spriteRenderer.color = color;
            // if(_spriteRenderer.material.HasFloat("_HurtColor")){
            //     _spriteRenderer.material.SetFloat("_HurtColor", value);
            // }
        }
    }

    override public Vector2 GetHitBorderNoraml(ref Vector3 targetPos)
    {
        return Vector2.zero;//todo monster.GetHitBorderNoraml
    }

    internal void Die()
    {
        if (isDie)
            return;
        isDie = true;
        enbaleFire = false;

        if (_collider != null)
            _collider.enabled = false;

        bool bNeedPlayDieAni = true;
        if (_info != null)
        {
            if (!string.IsNullOrEmpty(_info.dieEffect))
            {
                PlayEffect(_info.dieEffect);
                bNeedPlayDieAni = false;
            }
            DropItem();

            if(_info.type == (int)MonsterType.Elite)
            {
                Platform.GetInstance().Vibrate(VibrateType.medium);
            }
            else if(_info.type == (int)MonsterType.Boss)
            {
                Platform.GetInstance().Vibrate(VibrateType.heavy);
            }
        }

        if (_gateVo != null)
        {
            this._gateVo.dieCount++;
        }
        
        RemoveWeapons();
        if (!bNeedPlayDieAni)
        {
            OnDie();
            Remove();
            return;
        }

        PlayDieAni();
    }

    async internal void PlayDieAni()
    {
        // if (_spriteRenderer != null && _spriteRenderer.materials != null && _spriteRenderer.material.HasFloat("_Strongth"))
        // {
        //     if (_animator != null)
        //     {
        //         _animator.enabled = false;
        //     }

        //     var material = _spriteRenderer.material;
        //     GTween.To(1f, 0.3f, 0.2f).OnUpdate((tweener) => {
        //         material.SetFloat("_Strongth", (float)tweener.value.d);
        //     }).SetDelay(0.2f).OnComplete(() => {
        //         if (this == null)
        //             return;
        //         OnDie();
        //         Remove();
        //     });
        // }
        // else
        // {
        PlayAction(AniNames.Die);
        await UniTask.Delay((int)(GetCurActionDuration() * 1000f));
        if (this == null)
            return;

        OnDie();
        Remove();
        // }
    }

    private float GetCurActionDuration()
    {
        if (_isUseSpine)
        {
            if (_animatorSpine == null)
                return 0;
            var time = _animatorSpine.AnimationState.GetCurrent(0).Animation.Duration;
            return time;
        }

        if (_animator == null)
            return 0;
        return _animator.GetCurrentAnimatorStateInfo(0).length;
    }

    private void PlayAction(string aniName, bool isLoop = false)
    {
        if (_isUseSpine)
        {
            if (_animatorSpine == null)
                return;

            _animatorSpine.Play(aniName, isLoop);
            if (aniName == AniNames.Attack)
            {
                _animatorSpine.Append(AniNames.Idle, true);
            }
        }
        else
        {
            if (_animator == null)
                return;
            _animator.Play(aniName, 0, 0);
            _animator.Update(0);
        }
    }

    private void StopAction(bool bStop)
    {
        if (_isUseSpine)
        {
            if (ReferenceEquals(_animatorSpine, null))
            {
                return;
            }

            if (bStop) {
                _animatorSpine.timeScale = 0f;
            } else {
                _animatorSpine.timeScale = this._initAniTimeScale;
            }
        }
        else
        {
            if (ReferenceEquals(_animator, null))
            {
                return;
            }

            if (_animator.enabled == !bStop) {
                return;
            }
            _animator.enabled = !bStop;
        }
    }


    protected virtual void OnDie() { }

    /// <summary>
    /// 掉落
    /// </summary>
    internal void DropItem()
    {
        if (_gateInfo == null || _gateInfo.dropId <= 0)
            return;

        var dropItems = ConfigDropItem.GetDropItems(_gateInfo.dropId);
        var dropPos = transform.position + new Vector3(Random.Range(-0.4f, 0.4f), Random.Range(-0.5f, 0.5f), transform.position.z);

        BattleDropManager.Inst.DropItems(dropPos, dropItems);

        DropAddSkill();
    }

    /// <summary>
    /// 掉落秘宝
    /// </summary>
    void DropAddSkill()
    {
        if (_info == null)
            return;

        if (_info.dropAddSkillId <= 0)
            return;

        if (Random.value >= _info.dropAddSkillPro)
            return;

        int dropCount = BattleUnitManager.Inst.player.GetAttrValue(BattleAttrType.AddSkillExternDropLimitCount);
        if (dropCount <= 0)
            return;

        BattleUnitManager.Inst.player.SetAttrValue(BattleAttrType.AddSkillExternDropLimitCount, dropCount - 1);
        var dropItems = ConfigDropItem.GetDropItems(_info.dropAddSkillId);
        var dropPos = transform.position + new Vector3(UnityEngine.Random.Range(-0.4f, 0.4f), UnityEngine.Random.Range(-0.2f, 0.2f), 0f);
        BattleDropManager.Inst.DropItems(dropPos, dropItems);
    }


    internal void Remove()
    {
        BattleUnitManager.Inst.RemoveMonster(this);
    }


    /// <summary>
    /// 碰撞到目标
    /// </summary>
    /// <param name="target"></param>
    internal void OnCollisionTarget(IBattleObject target)
    {
        if (isDie)
            return;

        if (Time.realtimeSinceStartup - _preHitTime >= GetAttackCd())
        {
            _preHitTime = Time.realtimeSinceStartup;
            var multiplier = ComputeCollisionDamageMultiplier();
            var damage = GetCollisonDamage() * multiplier;
            target.OnHurt((int)damage, multiplier);
        }

        if (CollisionNeedDie())
        {
            if (this.IsNormal())
            {
                int addExp = (int)(GetExp() * BattleUnitManager.Inst.collisionExpPercent);
                var curHandler = (GameMain.curHandler as BattleHandler);
                if (curHandler != null)
                {
                    if (curHandler.GetAddExp() > 0)
                    {
                        addExp = (int)(GetExp() * BattleUnitManager.Inst.collisionExpPercent * curHandler.GetAddExp());
                    }
                }
                
                BattleUnitManager.Inst.player.CollectExp(addExp);
            }
            Die();
        }
    }

    /// <summary>
    /// 播放攻击动画
    /// </summary>
    /// <param name="aniName"></param>
    override public void PlayAttackAni(string aniName = null)
    {
        if (string.IsNullOrEmpty(aniName))
        {
            PlayAction(AniNames.Attack);
        }
        else
        {
            PlayAction(aniName);
        }
    }
}