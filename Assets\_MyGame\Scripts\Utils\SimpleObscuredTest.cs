using System;
using UnityEngine;
using UnityEngine.Assertions;
using MyGame.Utils;

namespace MyGame.Utils
{
    /// <summary>
    /// SimpleObscured 类的测试脚本
    /// </summary>
    public class SimpleObscuredTest : MonoBehaviour
    {
        [Header("测试用的加密数值")]
        public SimpleObscuredInt testInt = 100;
        public SimpleObscuredLong testLong = 1000L;
        public SimpleObscuredFloat testFloat = 3.14f;
        public SimpleObscuredDouble testDouble = 3.141592653589793;
        
        private void Start()
        {
            RunTests();
        }
        
        private void RunTests()
        {
            Debug.Log("=== SimpleObscured 测试开始 ===");

            try
            {
                TestBasicOperations();
                TestArithmeticOperations();
                TestComparisonOperations();
                TestEncryptionDecryption();
                TestFloatOperations();
                TestDoubleOperations();
                TestSerialization();

                Debug.Log("<color=green>=== ✓ 所有测试通过！SimpleObscured 功能正常 ===</color>");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"<color=red>=== ✗ 测试失败: {e.Message} ===</color>");
                Debug.LogException(e);
            }
        }
        
        private void TestBasicOperations()
        {
            Debug.Log("--- 基本操作测试 ---");

            // 赋值和读取
            SimpleObscuredInt intVal = 42;
            SimpleObscuredLong longVal = 123456789L;

            Assert.AreEqual(42, (int)intVal, "Int 赋值测试失败");
            Assert.AreEqual(123456789L, (long)longVal, "Long 赋值测试失败");
            Debug.Log($"✓ 赋值测试通过 - Int: {intVal}, Long: {longVal}");

            // 隐式转换
            int normalInt = intVal;
            long normalLong = longVal;

            Assert.AreEqual(42, normalInt, "Int 隐式转换失败");
            Assert.AreEqual(123456789L, normalLong, "Long 隐式转换失败");
            Debug.Log($"✓ 隐式转换测试通过 - Int: {normalInt}, Long: {normalLong}");

            // 显式获取
            int explicitInt = intVal.GetDecrypted();
            long explicitLong = longVal.GetDecrypted();

            Assert.AreEqual(42, explicitInt, "Int 显式获取失败");
            Assert.AreEqual(123456789L, explicitLong, "Long 显式获取失败");
            Debug.Log($"✓ 显式获取测试通过 - Int: {explicitInt}, Long: {explicitLong}");
        }
        
        private void TestArithmeticOperations()
        {
            Debug.Log("--- 算术操作测试 ---");

            SimpleObscuredInt a = 10;
            SimpleObscuredInt b = 5;

            Assert.AreEqual(15, (int)(a + b), "加法运算失败");
            Assert.AreEqual(5, (int)(a - b), "减法运算失败");
            Assert.AreEqual(50, (int)(a * b), "乘法运算失败");
            Assert.AreEqual(2, (int)(a / b), "除法运算失败");
            Assert.AreEqual(0, (int)(a % b), "取模运算失败");
            Debug.Log($"✓ 基本算术运算测试通过");

            // 自增自减
            SimpleObscuredInt c = 10;
            Assert.AreEqual(10, (int)c, "初始值错误");
            c++;
            Assert.AreEqual(11, (int)c, "自增运算失败");
            c--;
            Assert.AreEqual(10, (int)c, "自减运算失败");
            Debug.Log($"✓ 自增自减测试通过");

            // 复合赋值
            c += 5;
            Assert.AreEqual(15, (int)c, "复合赋值失败");
            Debug.Log($"✓ 复合赋值测试通过");
        }
        
        private void TestComparisonOperations()
        {
            Debug.Log("--- 比较操作测试 ---");

            SimpleObscuredInt a = 10;
            SimpleObscuredInt b = 5;
            SimpleObscuredInt c = 10;

            Assert.IsTrue(a == c, "相等测试失败");
            Assert.IsTrue(a != b, "不等测试失败");
            Assert.IsTrue(a > b, "大于测试失败");
            Assert.IsTrue(b < a, "小于测试失败");
            Assert.IsTrue(a >= c, "大于等于测试失败");
            Assert.IsTrue(b <= a, "小于等于测试失败");
            Debug.Log($"✓ 基本比较运算测试通过");

            // 与普通数值比较
            Assert.IsTrue(a > 8, "与普通数值比较失败");
            Assert.IsTrue(a == 10, "与普通数值相等比较失败");
            Debug.Log($"✓ 与普通数值比较测试通过");
        }
        
        private void TestEncryptionDecryption()
        {
            Debug.Log("--- 加密解密测试 ---");

            int originalValue = 12345;
            int key = SimpleObscuredInt.GenerateKey();

            int encrypted = SimpleObscuredInt.Encrypt(originalValue, key);
            int decrypted = SimpleObscuredInt.Decrypt(encrypted, key);

            Assert.AreEqual(originalValue, decrypted, "加密解密过程失败");
            Assert.AreNotEqual(originalValue, encrypted, "加密值不应该等于原始值");
            Debug.Log($"✓ 加密解密正确性测试通过 (原始: {originalValue}, 加密: {encrypted}, 解密: {decrypted})");

            // 测试密钥随机化
            SimpleObscuredInt testVal = originalValue;
            int valueBefore = testVal.GetDecrypted();
            testVal.RandomizeCryptoKey();
            int valueAfter = testVal.GetDecrypted();

            Assert.AreEqual(valueBefore, valueAfter, "密钥随机化后值发生了变化");
            Debug.Log($"✓ 密钥随机化测试通过 (值保持不变: {valueAfter})");
        }

        private void TestFloatOperations()
        {
            Debug.Log("--- 浮点数操作测试 ---");

            // 基本赋值和读取
            SimpleObscuredFloat floatVal = 3.14f;
            Assert.AreEqual(3.14f, (float)floatVal, "Float 赋值测试失败");
            Debug.Log($"✓ Float 赋值测试通过: {floatVal}");

            // 算术运算
            SimpleObscuredFloat a = 10.5f;
            SimpleObscuredFloat b = 2.5f;

            Assert.AreEqual(13.0f, (float)(a + b), "Float 加法失败");
            Assert.AreEqual(8.0f, (float)(a - b), "Float 减法失败");
            Assert.AreEqual(26.25f, (float)(a * b), "Float 乘法失败");
            Assert.AreEqual(4.2f, (float)(a / b), "Float 除法失败");
            Debug.Log($"✓ Float 算术运算测试通过");

            // 比较运算（考虑浮点精度）
            SimpleObscuredFloat c = 10.5f;
            Assert.IsTrue(a == c, "Float 相等比较失败");
            Assert.IsTrue(a > b, "Float 大于比较失败");
            Assert.IsTrue(b < a, "Float 小于比较失败");
            Debug.Log($"✓ Float 比较运算测试通过");

            // 加密解密测试
            float originalFloat = 123.456f;
            int key = SimpleObscuredFloat.GenerateKey();
            int encrypted = SimpleObscuredFloat.Encrypt(originalFloat, key);
            float decrypted = SimpleObscuredFloat.Decrypt(encrypted, key);

            Assert.AreEqual(originalFloat, decrypted, "Float 加密解密失败");
            Debug.Log($"✓ Float 加密解密测试通过");

            // 密钥随机化
            SimpleObscuredFloat testFloat = originalFloat;
            float valueBefore = testFloat.GetDecrypted();
            testFloat.RandomizeCryptoKey();
            float valueAfter = testFloat.GetDecrypted();

            Assert.AreEqual(valueBefore, valueAfter, "Float 密钥随机化后值发生变化");
            Debug.Log($"✓ Float 密钥随机化测试通过");
        }

        private void TestDoubleOperations()
        {
            Debug.Log("--- 双精度浮点数操作测试 ---");

            // 基本赋值和读取
            SimpleObscuredDouble doubleVal = 3.141592653589793;
            Assert.AreEqual(3.141592653589793, (double)doubleVal, "Double 赋值测试失败");
            Debug.Log($"✓ Double 赋值测试通过: {doubleVal}");

            // 算术运算
            SimpleObscuredDouble a = 10.123456789;
            SimpleObscuredDouble b = 2.987654321;

            // 使用近似比较来处理浮点精度问题
            Assert.IsTrue(Math.Abs((double)(a + b) - 13.111111110) < 0.000000001, "Double 加法失败");
            Assert.IsTrue(Math.Abs((double)(a - b) - 7.135802468) < 0.000000001, "Double 减法失败");
            Assert.IsTrue(Math.Abs((double)(a * b) - 30.246913578) < 0.000000001, "Double 乘法失败");
            Assert.IsTrue(Math.Abs((double)(a / b) - 3.388888889) < 0.000000001, "Double 除法失败");
            Debug.Log($"✓ Double 算术运算测试通过");

            // 比较运算（考虑双精度浮点精度）
            SimpleObscuredDouble c = 10.123456789;
            Assert.IsTrue(a == c, "Double 相等比较失败");
            Assert.IsTrue(a > b, "Double 大于比较失败");
            Assert.IsTrue(b < a, "Double 小于比较失败");
            Debug.Log($"✓ Double 比较运算测试通过");

            // 加密解密测试
            double originalDouble = 123.456789012345;
            long key = SimpleObscuredDouble.GenerateKey();
            long encrypted = SimpleObscuredDouble.Encrypt(originalDouble, key);
            double decrypted = SimpleObscuredDouble.Decrypt(encrypted, key);

            Assert.AreEqual(originalDouble, decrypted, "Double 加密解密失败");
            Debug.Log($"✓ Double 加密解密测试通过");

            // 密钥随机化
            SimpleObscuredDouble testDouble = originalDouble;
            double valueBefore = testDouble.GetDecrypted();
            testDouble.RandomizeCryptoKey();
            double valueAfter = testDouble.GetDecrypted();

            Assert.AreEqual(valueBefore, valueAfter, "Double 密钥随机化后值发生变化");
            Debug.Log($"✓ Double 密钥随机化测试通过");

            // 高精度测试
            SimpleObscuredDouble highPrecision = Math.PI;
            Assert.AreEqual(Math.PI, (double)highPrecision, "高精度Double测试失败");
            Debug.Log($"✓ 高精度Double测试通过: {highPrecision.ToString("F15")}");
        }

        private void TestSerialization()
        {
            Debug.Log("--- 序列化测试 ---");

            // 验证初始序列化值
            Assert.AreEqual(100, (int)testInt, "序列化的Int初始值错误");
            Assert.AreEqual(1000L, (long)testLong, "序列化的Long初始值错误");
            Assert.AreEqual(3.14f, (float)testFloat, "序列化的Float初始值错误");
            Assert.AreEqual(3.141592653589793, (double)testDouble, "序列化的Double初始值错误");
            Debug.Log($"✓ 序列化初始值正确 - Int: {testInt}, Long: {testLong}, Float: {testFloat}, Double: {testDouble}");

            // 修改值并验证
            testInt = 999;
            testLong = 888888L;
            testFloat = 2.718f;
            testDouble = Math.E;

            Assert.AreEqual(999, (int)testInt, "修改后的Int值错误");
            Assert.AreEqual(888888L, (long)testLong, "修改后的Long值错误");
            Assert.AreEqual(2.718f, (float)testFloat, "修改后的Float值错误");
            Assert.AreEqual(Math.E, (double)testDouble, "修改后的Double值错误");
            Debug.Log($"✓ 序列化值修改测试通过 - Int: {testInt}, Long: {testLong}, Float: {testFloat}, Double: {testDouble}");
        }
        
        [ContextMenu("运行性能测试")]
        private void PerformanceTest()
        {
            Debug.Log("--- 性能测试 ---");
            
            const int iterations = 100000;
            
            // 普通int性能测试
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            int normalSum = 0;
            for (int i = 0; i < iterations; i++)
            {
                normalSum += i;
            }
            stopwatch.Stop();
            long normalTime = stopwatch.ElapsedMilliseconds;
            
            // SimpleObscuredInt性能测试
            stopwatch.Restart();
            SimpleObscuredInt obscuredSum = 0;
            for (int i = 0; i < iterations; i++)
            {
                obscuredSum += i;
            }
            stopwatch.Stop();
            long obscuredTime = stopwatch.ElapsedMilliseconds;
            
            // 验证计算结果正确性
            Assert.AreEqual(normalSum, (int)obscuredSum, "加密和普通计算结果不一致");

            Debug.Log($"普通int累加 {iterations} 次耗时: {normalTime}ms");
            Debug.Log($"SimpleObscuredInt累加 {iterations} 次耗时: {obscuredTime}ms");
            Debug.Log($"性能比率: {(float)obscuredTime / normalTime:F2}x");
            Debug.Log($"✓ 结果验证通过 - 普通: {normalSum}, 加密: {(int)obscuredSum}");
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.T))
            {
                RunTests();
            }
            
            if (Input.GetKeyDown(KeyCode.P))
            {
                PerformanceTest();
            }
        }
    }
}
