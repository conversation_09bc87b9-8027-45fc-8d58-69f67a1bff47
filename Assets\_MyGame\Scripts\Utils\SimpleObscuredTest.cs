using UnityEngine;
using MyGame.Utils;

namespace MyGame.Utils
{
    /// <summary>
    /// SimpleObscured 类的测试脚本
    /// </summary>
    public class SimpleObscuredTest : MonoBehaviour
    {
        [Header("测试用的加密数值")]
        public SimpleObscuredInt testInt = 100;
        public SimpleObscuredLong testLong = 1000L;
        
        private void Start()
        {
            RunTests();
        }
        
        private void RunTests()
        {
            Debug.Log("=== SimpleObscured 测试开始 ===");
            
            TestBasicOperations();
            TestArithmeticOperations();
            TestComparisonOperations();
            TestEncryptionDecryption();
            TestSerialization();
            
            Debug.Log("=== SimpleObscured 测试完成 ===");
        }
        
        private void TestBasicOperations()
        {
            Debug.Log("--- 基本操作测试 ---");
            
            // 赋值和读取
            SimpleObscuredInt intVal = 42;
            SimpleObscuredLong longVal = 123456789L;
            
            Debug.Log($"Int 赋值测试: {intVal} (期望: 42)");
            Debug.Log($"Long 赋值测试: {longVal} (期望: 123456789)");
            
            // 隐式转换
            int normalInt = intVal;
            long normalLong = longVal;
            
            Debug.Log($"隐式转换测试 - Int: {normalInt}, Long: {normalLong}");
            
            // 显式获取
            int explicitInt = intVal.GetDecrypted();
            long explicitLong = longVal.GetDecrypted();
            
            Debug.Log($"显式获取测试 - Int: {explicitInt}, Long: {explicitLong}");
        }
        
        private void TestArithmeticOperations()
        {
            Debug.Log("--- 算术操作测试 ---");
            
            SimpleObscuredInt a = 10;
            SimpleObscuredInt b = 5;
            
            Debug.Log($"加法: {a} + {b} = {a + b} (期望: 15)");
            Debug.Log($"减法: {a} - {b} = {a - b} (期望: 5)");
            Debug.Log($"乘法: {a} * {b} = {a * b} (期望: 50)");
            Debug.Log($"除法: {a} / {b} = {a / b} (期望: 2)");
            Debug.Log($"取模: {a} % {b} = {a % b} (期望: 0)");
            
            // 自增自减
            SimpleObscuredInt c = 10;
            Debug.Log($"自增前: {c}");
            c++;
            Debug.Log($"自增后: {c} (期望: 11)");
            c--;
            Debug.Log($"自减后: {c} (期望: 10)");
            
            // 复合赋值
            c += 5;
            Debug.Log($"复合赋值 +=: {c} (期望: 15)");
        }
        
        private void TestComparisonOperations()
        {
            Debug.Log("--- 比较操作测试 ---");
            
            SimpleObscuredInt a = 10;
            SimpleObscuredInt b = 5;
            SimpleObscuredInt c = 10;
            
            Debug.Log($"相等测试: {a} == {c} = {a == c} (期望: True)");
            Debug.Log($"不等测试: {a} != {b} = {a != b} (期望: True)");
            Debug.Log($"大于测试: {a} > {b} = {a > b} (期望: True)");
            Debug.Log($"小于测试: {b} < {a} = {b < a} (期望: True)");
            Debug.Log($"大于等于测试: {a} >= {c} = {a >= c} (期望: True)");
            Debug.Log($"小于等于测试: {b} <= {a} = {b <= a} (期望: True)");
            
            // 与普通数值比较
            Debug.Log($"与普通数值比较: {a} > 8 = {a > 8} (期望: True)");
        }
        
        private void TestEncryptionDecryption()
        {
            Debug.Log("--- 加密解密测试 ---");
            
            int originalValue = 12345;
            int key = SimpleObscuredInt.GenerateKey();
            
            int encrypted = SimpleObscuredInt.Encrypt(originalValue, key);
            int decrypted = SimpleObscuredInt.Decrypt(encrypted, key);
            
            Debug.Log($"原始值: {originalValue}");
            Debug.Log($"密钥: {key}");
            Debug.Log($"加密值: {encrypted}");
            Debug.Log($"解密值: {decrypted}");
            Debug.Log($"加密解密正确性: {originalValue == decrypted}");
            
            // 测试密钥随机化
            SimpleObscuredInt testVal = originalValue;
            Debug.Log($"随机化前: {testVal}");
            testVal.RandomizeCryptoKey();
            Debug.Log($"随机化后: {testVal} (值应该保持不变)");
        }
        
        private void TestSerialization()
        {
            Debug.Log("--- 序列化测试 ---");
            
            // 这些值在Inspector中应该显示为解密后的值
            Debug.Log($"序列化的Int值: {testInt}");
            Debug.Log($"序列化的Long值: {testLong}");
            
            // 修改值
            testInt = 999;
            testLong = 888888L;
            
            Debug.Log($"修改后的Int值: {testInt}");
            Debug.Log($"修改后的Long值: {testLong}");
        }
        
        [ContextMenu("运行性能测试")]
        private void PerformanceTest()
        {
            Debug.Log("--- 性能测试 ---");
            
            const int iterations = 100000;
            
            // 普通int性能测试
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            int normalSum = 0;
            for (int i = 0; i < iterations; i++)
            {
                normalSum += i;
            }
            stopwatch.Stop();
            long normalTime = stopwatch.ElapsedMilliseconds;
            
            // SimpleObscuredInt性能测试
            stopwatch.Restart();
            SimpleObscuredInt obscuredSum = 0;
            for (int i = 0; i < iterations; i++)
            {
                obscuredSum += i;
            }
            stopwatch.Stop();
            long obscuredTime = stopwatch.ElapsedMilliseconds;
            
            Debug.Log($"普通int累加 {iterations} 次耗时: {normalTime}ms");
            Debug.Log($"SimpleObscuredInt累加 {iterations} 次耗时: {obscuredTime}ms");
            Debug.Log($"性能比率: {(float)obscuredTime / normalTime:F2}x");
            Debug.Log($"结果验证 - 普通: {normalSum}, 加密: {(int)obscuredSum}");
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.T))
            {
                RunTests();
            }
            
            if (Input.GetKeyDown(KeyCode.P))
            {
                PerformanceTest();
            }
        }
    }
}
