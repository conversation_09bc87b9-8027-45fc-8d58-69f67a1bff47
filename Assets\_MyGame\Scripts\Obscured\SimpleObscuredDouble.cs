using System;
using System.Runtime.InteropServices;
using UnityEngine;


/// <summary>
/// 简化版本的ObscuredDouble，用于热更代码中的双精度浮点数值保护
/// </summary>
[Serializable]
public struct SimpleObscuredDouble : IEquatable<SimpleObscuredDouble>, IEquatable<double>, IComparable<SimpleObscuredDouble>, IComparable<double>, IComparable
{
    [SerializeField] private long hiddenValue;
    [SerializeField] private long currentCryptoKey;

    private SimpleObscuredDouble(double value)
    {
        currentCryptoKey = GenerateKey();
        hiddenValue = Encrypt(value, currentCryptoKey);
    }

    /// <summary>
    /// 加密双精度浮点数值
    /// </summary>
    public static long Encrypt(double value, long key)
    {
        var union = new DoubleLongUnion { doubleValue = value };
        return union.longValue ^ key;
    }

    /// <summary>
    /// 解密双精度浮点数值
    /// </summary>
    public static double Decrypt(long value, long key)
    {
        var union = new DoubleLongUnion { longValue = value ^ key };
        return union.doubleValue;
    }

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    public static long GenerateKey()
    {
        return System.DateTime.Now.Ticks;
    }

    /// <summary>
    /// 获取解密后的值
    /// </summary>
    public double GetDecrypted()
    {
        return InternalDecrypt();
    }

    /// <summary>
    /// 随机化密钥
    /// </summary>
    public void RandomizeCryptoKey()
    {
        var decrypted = InternalDecrypt();
        currentCryptoKey = GenerateKey();
        hiddenValue = Encrypt(decrypted, currentCryptoKey);
    }

    private double InternalDecrypt()
    {
        if (IsDefault()) 
            return 0.0;
        
        return Decrypt(hiddenValue, currentCryptoKey);
    }

    private bool IsDefault()
    {
        return hiddenValue == default && currentCryptoKey == default;
    }

    /// <summary>
    /// 比较两个双精度浮点数是否相等（考虑浮点精度）
    /// </summary>
    private static bool Compare(double a, double b)
    {
        // 使用相对误差和绝对误差的组合来比较
        if (a == b) return true;

        double tolerance = Math.Max(Math.Abs(a), Math.Abs(b)) * 1e-15;
        if (tolerance < 1e-15) tolerance = 1e-15;

        return Math.Abs(a - b) <= tolerance;
    }

    // 隐式转换操作符
    public static implicit operator SimpleObscuredDouble(double value)
    {
        return new SimpleObscuredDouble(value);
    }

    public static implicit operator double(SimpleObscuredDouble value)
    {
        return value.InternalDecrypt();
    }

    // 算术操作符
    public static SimpleObscuredDouble operator ++(SimpleObscuredDouble input)
    {
        return input.InternalDecrypt() + 1.0;
    }

    public static SimpleObscuredDouble operator --(SimpleObscuredDouble input)
    {
        return input.InternalDecrypt() - 1.0;
    }

    public static SimpleObscuredDouble operator +(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        return a.InternalDecrypt() + b.InternalDecrypt();
    }

    public static SimpleObscuredDouble operator -(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        return a.InternalDecrypt() - b.InternalDecrypt();
    }

    public static SimpleObscuredDouble operator *(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        return a.InternalDecrypt() * b.InternalDecrypt();
    }

    public static SimpleObscuredDouble operator /(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        return a.InternalDecrypt() / b.InternalDecrypt();
    }

    public static SimpleObscuredDouble operator %(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        return a.InternalDecrypt() % b.InternalDecrypt();
    }

    // 比较操作符
    public static bool operator ==(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        return Compare(a.InternalDecrypt(), b.InternalDecrypt());
    }

    public static bool operator !=(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        return !Compare(a.InternalDecrypt(), b.InternalDecrypt());
    }

    public static bool operator >(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        return a.InternalDecrypt() > b.InternalDecrypt();
    }

    public static bool operator <(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        return a.InternalDecrypt() < b.InternalDecrypt();
    }

    public static bool operator >=(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        var aVal = a.InternalDecrypt();
        var bVal = b.InternalDecrypt();
        return aVal > bVal || Compare(aVal, bVal);
    }

    public static bool operator <=(SimpleObscuredDouble a, SimpleObscuredDouble b)
    {
        var aVal = a.InternalDecrypt();
        var bVal = b.InternalDecrypt();
        return aVal < bVal || Compare(aVal, bVal);
    }

    // 接口实现
    public bool Equals(SimpleObscuredDouble other)
    {
        return Compare(InternalDecrypt(), other.InternalDecrypt());
    }

    public bool Equals(double other)
    {
        return Compare(InternalDecrypt(), other);
    }

    public override bool Equals(object obj)
    {
        if (obj is SimpleObscuredDouble obscured)
            return Equals(obscured);
        if (obj is double doubleValue)
            return Equals(doubleValue);
        return false;
    }

    public override int GetHashCode()
    {
        return InternalDecrypt().GetHashCode();
    }

    public int CompareTo(SimpleObscuredDouble other)
    {
        return InternalDecrypt().CompareTo(other.InternalDecrypt());
    }

    public int CompareTo(double other)
    {
        return InternalDecrypt().CompareTo(other);
    }

    public int CompareTo(object obj)
    {
        if (obj is SimpleObscuredDouble obscured)
            return CompareTo(obscured);
        if (obj is double doubleValue)
            return CompareTo(doubleValue);
        throw new ArgumentException("Object must be of type SimpleObscuredDouble or double");
    }

    public override string ToString()
    {
        return InternalDecrypt().ToString();
    }

    public string ToString(string format)
    {
        return InternalDecrypt().ToString(format);
    }

    public string ToString(IFormatProvider provider)
    {
        return InternalDecrypt().ToString(provider);
    }

    public string ToString(string format, IFormatProvider provider)
    {
        return InternalDecrypt().ToString(format, provider);
    }

    /// <summary>
    /// 用于double和long之间的位级转换的联合体
    /// </summary>
    [StructLayout(LayoutKind.Explicit)]
    private struct DoubleLongUnion
    {
        [FieldOffset(0)] public double doubleValue;
        [FieldOffset(0)] public long longValue;
    }
}

