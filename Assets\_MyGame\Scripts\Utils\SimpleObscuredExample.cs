using UnityEngine;
using MyGame.Utils;

namespace MyGame.Utils
{
    /// <summary>
    /// SimpleObscuredInt和SimpleObscuredLong的使用示例
    /// </summary>
    public class SimpleObscuredExample : MonoBehaviour
    {
        [Header("简化版本的加密数值示例")]
        [SerializeField] private SimpleObscuredInt playerScore = 1000;
        [SerializeField] private SimpleObscuredLong playerMoney = 50000L;
        [SerializeField] private SimpleObscuredFloat playerSpeed = 5.5f;
        [SerializeField] private SimpleObscuredDouble playerPrecision = 3.141592653589793;
        
        private void Start()
        {
            // 基本使用示例
            Debug.Log("=== SimpleObscured 使用示例 ===");
            
            // 直接赋值和读取
            playerScore = 2000;
            Debug.Log($"玩家分数: {playerScore}");
            
            playerMoney = 100000L;
            Debug.Log($"玩家金币: {playerMoney}");

            playerSpeed = 7.8f;
            Debug.Log($"玩家速度: {playerSpeed}");

            playerPrecision = System.Math.PI;
            Debug.Log($"高精度数值: {playerPrecision}");

            // 算术运算
            playerScore += 500;
            Debug.Log($"增加500分后: {playerScore}");

            playerMoney *= 2;
            Debug.Log($"金币翻倍后: {playerMoney}");

            playerSpeed += 1.5f;
            Debug.Log($"速度提升后: {playerSpeed}");

            playerPrecision *= 2.0;
            Debug.Log($"精度值翻倍后: {playerPrecision}");
            
            // 比较运算
            if (playerScore > 2000)
            {
                Debug.Log("分数超过2000!");
            }
            
            if (playerMoney >= 200000L)
            {
                Debug.Log("金币达到20万!");
            }
            
            // 随机化密钥（增强安全性）
            playerScore.RandomizeCryptoKey();
            playerMoney.RandomizeCryptoKey();
            playerSpeed.RandomizeCryptoKey();
            playerPrecision.RandomizeCryptoKey();
            Debug.Log("密钥已随机化");

            // 显式获取解密值
            int currentScore = playerScore.GetDecrypted();
            long currentMoney = playerMoney.GetDecrypted();
            float currentSpeed = playerSpeed.GetDecrypted();
            double currentPrecision = playerPrecision.GetDecrypted();
            Debug.Log($"显式获取 - 分数: {currentScore}, 金币: {currentMoney}, 速度: {currentSpeed}, 精度: {currentPrecision}");
            
            // 与普通数值的运算
            int bonus = 1000;
            playerScore = playerScore + bonus;
            Debug.Log($"加上奖励后分数: {playerScore}");
            
            // 字符串格式化
            Debug.Log($"格式化分数: {playerScore.ToString("N0")}");
            Debug.Log($"格式化金币: {playerMoney.ToString("C")}");
            Debug.Log($"格式化速度: {playerSpeed.ToString("F2")} m/s");
            Debug.Log($"格式化精度值: {playerPrecision.ToString("F15")}");
        }
        
        /// <summary>
        /// 演示在游戏逻辑中的实际使用
        /// </summary>
        public void GameLogicExample()
        {
            // 模拟游戏中的数值操作
            
            // 获得分数
            AddScore(100);
            
            // 消费金币
            if (SpendMoney(5000))
            {
                Debug.Log("购买成功!");
            }
            else
            {
                Debug.Log("金币不足!");
            }
            
            // 检查是否达到某个里程碑
            CheckMilestone();
        }
        
        private void AddScore(int points)
        {
            playerScore += points;
            Debug.Log($"获得 {points} 分，当前分数: {playerScore}");
        }
        
        private bool SpendMoney(long amount)
        {
            if (playerMoney >= amount)
            {
                playerMoney -= amount;
                Debug.Log($"消费 {amount} 金币，剩余: {playerMoney}");
                return true;
            }
            return false;
        }
        
        private void CheckMilestone()
        {
            // 使用加密数值进行条件判断
            if (playerScore >= 10000)
            {
                Debug.Log("达成分数里程碑: 10000分!");
            }
            
            if (playerMoney >= 1000000L)
            {
                Debug.Log("达成财富里程碑: 100万金币!");
            }
        }
        
        /// <summary>
        /// 演示序列化和反序列化
        /// </summary>
        public void SerializationExample()
        {
            // 由于使用了[SerializeField]，这些值会被Unity自动序列化
            // 在Inspector中可以看到加密后的hiddenValue和currentCryptoKey
            // 但实际显示的值是解密后的结果
            
            Debug.Log("=== 序列化示例 ===");
            Debug.Log($"序列化前 - 分数: {playerScore}, 金币: {playerMoney}");
            
            // 模拟保存/加载过程
            // Unity会自动处理序列化，无需额外代码
        }
        
        private void Update()
        {
            // 演示运行时的使用
            if (Input.GetKeyDown(KeyCode.Space))
            {
                GameLogicExample();
            }
            
            if (Input.GetKeyDown(KeyCode.R))
            {
                // 随机化密钥以增强安全性
                playerScore.RandomizeCryptoKey();
                playerMoney.RandomizeCryptoKey();
                Debug.Log("密钥已重新随机化");
            }
        }
    }
}
