# SimpleObscured 数值保护类

## 概述

SimpleObscuredInt、SimpleObscuredLong、SimpleObscuredFloat 和 SimpleObscuredDouble 是从 CodeStage AntiCheatToolkit 中提取的简化版本数值保护类，专门用于热更代码中保护敏感数值不被内存修改器轻易篡改。

## 特性

- **内存保护**: 数值在内存中以加密形式存储
- **透明使用**: 支持隐式转换，使用方式与普通 int/long 相同
- **完整操作符**: 支持所有算术和比较操作符
- **Unity序列化**: 支持 Unity Inspector 显示和序列化
- **无外部依赖**: 不依赖 AntiCheatToolkit DLL，可在热更代码中使用

## 使用方法

### 基本声明

```csharp
using MyGame.Utils;

public class PlayerData : MonoBehaviour
{
    [SerializeField] private SimpleObscuredInt playerScore = 1000;
    [SerializeField] private SimpleObscuredLong playerMoney = 50000L;
    [SerializeField] private SimpleObscuredFloat playerSpeed = 5.5f;
    [SerializeField] private SimpleObscuredDouble playerPrecision = 3.141592653589793;
}
```

### 基本操作

```csharp
// 赋值
playerScore = 2000;
playerMoney = 100000L;
playerSpeed = 7.8f;
playerPrecision = System.Math.PI;

// 读取
int currentScore = playerScore;
long currentMoney = playerMoney;
float currentSpeed = playerSpeed;
double currentPrecision = playerPrecision;

// 算术运算
playerScore += 500;
playerMoney *= 2;
playerScore++;

// 比较运算
if (playerScore > 1500)
{
    Debug.Log("分数超过1500!");
}

if (playerMoney >= 200000L)
{
    Debug.Log("金币充足!");
}
```

### 高级功能

```csharp
// 显式获取解密值
int score = playerScore.GetDecrypted();
long money = playerMoney.GetDecrypted();

// 随机化密钥（增强安全性）
playerScore.RandomizeCryptoKey();
playerMoney.RandomizeCryptoKey();

// 字符串格式化
string formattedScore = playerScore.ToString("N0");
string formattedMoney = playerMoney.ToString("C");
```

## 安全机制

### 加密算法
使用简单但有效的 XOR + 加法组合加密：
```csharp
// 加密: (value ^ key) + key
// 解密: (encrypted - key) ^ key
```

### 密钥生成
- SimpleObscuredInt: 使用 System.Random 生成随机密钥
- SimpleObscuredLong: 组合两个 int 值生成 long 密钥
- SimpleObscuredFloat: 使用 System.Random 生成随机密钥
- SimpleObscuredDouble: 组合两个 int 值生成 long 密钥

### 加密算法
- **整数类型** (Int/Long): XOR + 加法组合加密
- **浮点类型** (Float): 使用位级联合体转换为 int 后进行 XOR 加密
- **双精度浮点类型** (Double): 使用位级联合体转换为 long 后进行 XOR 加密

### 内存保护
- 真实数值不直接存储在内存中
- 每个实例使用独立的随机密钥
- 支持运行时重新随机化密钥

## 与原版 ObscuredTypes 的区别

### 简化内容
- 移除了 ObscuredCheatingDetector 依赖
- 移除了 HashUtils 验证机制
- 移除了 ThreadSafeRandom 依赖
- 移除了 Honeypot 反作弊机制
- 简化了密钥生成逻辑

### 保留功能
- 核心加密/解密逻辑
- 完整的操作符重载
- Unity 序列化支持
- 类型转换支持
- 接口实现（IEquatable, IComparable 等）

## 性能考虑

- 每次访问都需要解密，比普通数值稍慢
- 建议在长期存储的敏感数值上使用
- 临时计算建议转换为普通数值进行

## 使用建议

### 适用场景
- 玩家分数、金币等重要数值
- 游戏进度、等级等关键数据
- 需要防止内存修改的任何数值

### 不适用场景
- 频繁计算的临时变量
- 性能敏感的循环内部
- 不重要的UI显示数值

### 最佳实践
```csharp
public class PlayerStats
{
    // 重要数值使用加密
    [SerializeField] private SimpleObscuredInt level = 1;
    [SerializeField] private SimpleObscuredLong experience = 0L;
    
    // 临时计算使用普通数值
    public void AddExperience(int amount)
    {
        long currentExp = experience; // 解密一次
        long newExp = currentExp + amount; // 普通运算
        experience = newExp; // 加密存储
        
        // 定期随机化密钥
        if (UnityEngine.Random.Range(0, 100) < 5) // 5% 概率
        {
            experience.RandomizeCryptoKey();
        }
    }
}
```

## 注意事项

1. **序列化兼容性**: 加密数值在 Inspector 中显示为解密后的值
2. **密钥管理**: 每个实例都有独立密钥，无需手动管理
3. **性能影响**: 比普通数值有轻微性能开销
4. **调试友好**: ToString() 方法返回解密后的值，便于调试

## 迁移指南

### 从普通数值迁移
```csharp
// 原代码
public int playerScore = 1000;
public long playerMoney = 50000L;

// 迁移后
public SimpleObscuredInt playerScore = 1000;
public SimpleObscuredLong playerMoney = 50000L;
```

### 从原版 ObscuredTypes 迁移
```csharp
// 原代码
using CodeStage.AntiCheat.ObscuredTypes;
public ObscuredInt playerScore = 1000;
public ObscuredLong playerMoney = 50000L;

// 迁移后
using MyGame.Utils;
public SimpleObscuredInt playerScore = 1000;
public SimpleObscuredLong playerMoney = 50000L;
```

大部分代码无需修改，只需更换命名空间和类型名称。
