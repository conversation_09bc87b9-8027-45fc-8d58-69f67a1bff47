﻿using Cysharp.Threading.Tasks;
using DG.Tweening;
using Proto.LogicData;
using Proto.Types;
using Spine;
using Spine.Unity;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Assertions;

/**
 * 战斗单位
 */
public class BattleUnit : BaseUnit, IBattleObject
{
    //攻击
    public SimpleObscuredInt attack { get; set; }
    //血量
    public SimpleObscuredLong curHp { get; set; }
    //最大基础血量，如果有其他加成，写在GetMaxHp方法内
    public SimpleObscuredLong maxHp { get; set; }
    //护盾血量
    public SimpleObscuredLong curShieldHp { get; set; }
    //最大护盾血量
    public SimpleObscuredLong maxShieldHp { get; set; }

    public float atkAddPercent { get; set; }

    protected WeaponManager _weaponManager;
    protected BuffManager _buffManager;
    protected BattleAttrMgr _battleAttrs;
    protected Transform _effectContainer;
    protected Dictionary<string, EffectAni> _dictAnis;

    protected float _lastUpdateTime100ms = 0f;

    protected List<string> _removedAni;


    override protected void OnAwake()
    {
        _battleAttrs = new BattleAttrMgr(this);
        _buffManager = new BuffManager();
        _weaponManager = new WeaponManager(this);
        _dictAnis = new Dictionary<string, EffectAni>();
        _effectContainer = this.transform.Find("effectContainer");
        _removedAni = new List<string>();
    }

    override protected void Clear()
    {
        RemoveAllEffectAnis();
        this.attack = 0;
        this.curHp = 0;
        this.maxHp = 0;
        this.moveSpeed = 0;
        this._lastUpdateTime100ms = 0f;
        this._battleAttrs.Clear();
        this._buffManager.Clear();
        this._weaponManager.Clear();
        _removedAni.Clear();
        base.Clear();
    }

    /// <summary>
    /// 改变血量
    /// </summary>
    /// <param name="value"></param>
    /// <param name="displayValue"></param>
    override public void ChangeHp(int value, bool displayValue = true, int relatedSkillLink = 0)
    {
        if (value > 0)
        {
            AddHp(value, displayValue);
        }
        else
        {
            OnHurt(-value);
        }
    }

    override public void AddHp(int value, bool displayAddValue = true)
    {
        this.curHp += value;
        if (curHp > GetMaxHp())
        {
            curHp = GetMaxHp();
        }
    }

    /**
     * 子弹命中
     */
    override public void OnBulletHit(BulletBase bullet)
    {
        float demage = bullet.GetDemage();
        float multiplier = bullet.ComputeCriticalDamageMultiplier();
        demage *= multiplier;
        this.Knockback(bullet.GetFirePos(), bullet.knockbackStrength, 0.15f);
        this.OnHurt((int)demage, multiplier, bullet.GetHitEffect());
        
    }

    /**
     * 受到伤害
     */
    override public void OnHurt(int damage, float damageMultiplier = 1f, string hitEffect = null) 
    {
    }

    /**
     * 击退
     */
    public virtual void Knockback(Vector3 attackerPos, float strength, float delay)
    {

    }


    /// <summary>
    /// 基础伤害值
    /// </summary>
    /// <returns></returns>
    override public float GetAttackValue()
    {
        return attack;
    }


    protected void Update()
    {
        OnUpdate100ms();
        OnUpdate();
    }

    virtual protected bool OnUpdate100ms()
    {
        float deltaTime = -1f;
        _lastUpdateTime100ms += Time.deltaTime;
        if (_lastUpdateTime100ms >= 0.1f)
        {
            //100ms更新次
            deltaTime = _lastUpdateTime100ms;
            _lastUpdateTime100ms = 0f;
        }
        if (deltaTime < 0f)
            return false;

        OnUpdateBuff(deltaTime);
        OnUpdateEffectAnis(deltaTime);
        return true;
    }

    virtual protected void OnUpdateBuff(float deltaTime)
    {
        _buffManager?.OnUpdate(deltaTime, this);
    }

    virtual protected void OnUpdate()
    {

    }


    /**
     * 方向
     */
    override public Vector3 GetDirection()
    {
        return this.transform.up;
    }

    /// <summary>
    /// 所有BUFF效果下的所有BUFF
    /// </summary>
    /// <param name="effect"></param>
    /// <returns></returns>
    override public List<BuffBase> GetBuffListByEffect(BuffEffectType effect)
    {
       return _buffManager.GetBuffListByEffect((int)effect);
    }

    /// <summary>
    /// BUFF效果整形数值累计和
    /// </summary>
    /// <param name="effect"></param>
    /// <param name="effectGroup"></param>
    /// <returns></returns>
    override public int GetBuffTotalValueByEffectGroup(BuffEffectType effect, int effectGroup)
    {
         return _buffManager.GetBuffTotalValue((int)effect, effectGroup);
    }

    /// <summary>
    /// BUFF效果浮点数值累计和
    /// </summary>
    /// <param name="effect"></param>
    /// <param name="effectGroup"></param>
    /// <returns></returns>
    override public float GetBuffTotalFloatValueByEffectGroup(BuffEffectType effect, int effectGroup)
    {
        return _buffManager.GetTotalFloatValue((int)effect, effectGroup);
    }

     /// <summary>
    /// BUFF效果浮点数值累计和
    /// </summary>
    /// <param name="effect"></param>
    /// <param name="effectGroup"></param>
    /// <returns></returns>
    override public float GetTotalPercentLimitByParam0(BuffEffectType effect, int effectGroup, int param0)
    {
        return _buffManager.GetTotalPercentLimitByParam0((int)effect, effectGroup, param0);
    }
  
    /**
     * 获取属性值 
     */
    override public int GetAttrValue(BattleAttrType type)
    {
        return _battleAttrs.GetAttrValue(type);
    }

    public Dictionary<BattleAttrType, int> GetAllAttrs()
    {
         return _battleAttrs.GetAllAttrDic();
    }

    /**
     * 设置属性值 
     */
    public virtual void SetAttrValue(BattleAttrType type, int value)
    {
        _battleAttrs.SetAttrValue(type, value);
    }

    /**
     * 获取属性值 
     */
    override public float GetAttrPercent(BattleAttrType type)
    {
        if (_battleAttrs == null)
        {
            return 0;
        }
        return _battleAttrs.GetAttrValue(type) / 10000f;
    }

    /**
     * 增加属性值，如果属性存在，则数量会增加
     */
    override public bool AddAttrValue(BattleAttrType type, int addValue)
    {
        _battleAttrs.AddAttrValue(type, addValue);
        return true;
    }

    /**
     * 添加Buff
     */
    override public BuffBase AddBuff(int buffId, float duationTimeAddPercent = 0f)
    {
        if (buffId <= 0)
            return null;

        var infoBuff = ConfigBuff.GetData(buffId);
        if (null == infoBuff)
            return null;

        if (IsIgnoreBuff(infoBuff))
            return null;

        var buff = this._buffManager.AddBuff(infoBuff, 1, duationTimeAddPercent);
        // Log.Debug("Battle Unit {0} Add Buff {0}", this.unitId, infoBuff.id);
        return buff;
    }

    virtual public bool IsIgnoreBuff(InfoBuff infoBuff)
    {
        return false;
    }

    virtual public void RemoveBuff(BuffEffectType type)
    {
        _buffManager.RemoveBuffByEffect((int)type);
    }

    /**
     * 装备武器
     */
    override public Weapon EquipWeapon(int weaponId)
    {
        if (weaponId <= 0)
        {
            return null;
        }
        return _weaponManager.Add(weaponId);
    }

    public void RemoveWeapon(int weaponId)
    {
        var info = ConfigWeapon.GetData(weaponId);
        if (info != null)
        {
            _weaponManager.Remove(info.weaponGroup);
        }
    }

    override public Weapon GetWeapon(int weaponGroup)
    {
        return _weaponManager.GetWeapon(weaponGroup);
    }

    /**
     * 移除武器
     */
    override public void RemoveWeaponByGroup(int weaponGroup)
    {
        _weaponManager.Remove(weaponGroup);
    }

    /// <summary>
    /// 添加一个动画特效
    /// </summary>
    /// <param name="effectName"></param>
    override public EffectAni AddEffectAni(string effectName)
    {
        EffectAni effect = null;
        if (!this._dictAnis.TryGetValue(effectName, out effect))
        {
            effect = PoolMgr.Inst.Get<EffectAni>(effectName);
            Assert.IsNotNull(effect);
            if (effect != null)
            {
                var buffOffsetAndScale = GetBuffEffectOffsetAndScale(effectName);
                Vector3 buffOffset = Vector3.zero;
                Vector3 buffScale = Vector3.one;
                if (buffOffsetAndScale.HasValue)
                {
                    buffOffset.x = buffOffsetAndScale.Value.x;
                    buffOffset.y = buffOffsetAndScale.Value.y;
                    buffScale.x = buffOffsetAndScale.Value.z;
                    buffScale.y = buffOffsetAndScale.Value.w;
                }

                effect.transform.localPosition = Vector3.zero;
                if (_effectContainer != null)
                {
                    effect.transform.parent = this._effectContainer;
                    effect.transform.position = this._effectContainer.position + buffOffset;
                    effect.transform.localScale = this._effectContainer.localScale * effect.GetInitScale() * buffScale;
                }
                else
                {
                    effect.transform.parent = this.transform;
                    effect.transform.position = this.transform.position + buffOffset;
                    effect.transform.localScale = effect.GetInitScale() * buffScale;

                }
                this._dictAnis.Add(effectName, effect);
            }
        }
      
        if (effect == null)
        {
            Log.Warning($"invalid effect res:{effectName}");
            return null;
        }

        effect.druationTime = 0f;
        return effect;
    }

    /// <summary>
    /// 移除动画特效
    /// </summary>
    /// <param name="effectName"></param>
    public void RemoveEffectAni(string effectName)
    {
        EffectAni effect = null;
        if (this._dictAnis.TryGetValue(effectName, out effect))
        {
            effect.druationTime = 0f;
            this._dictAnis.Remove(effectName);
            effect.Release();
        }
    }

    public override long GetMaxHp()
    {
        return this.maxHp;
    }

    protected virtual Vector4? GetBuffEffectOffsetAndScale(string effectName)
    {
        return null;
    }

    virtual protected void OnUpdateEffectAnis(float deltaTime)
    {
        if (_dictAnis == null)
            return;
        _removedAni.Clear();
        foreach (var effectAniInfo  in this._dictAnis)
        {
            var effectAni = effectAniInfo.Value;
            if (effectAni.IsForEever())
                //永久
                continue;
            effectAni.druationTime += deltaTime;
            if (effectAni.druationTime >= effectAni.lifeTime)
            {
                //生命周期结束
                effectAni.druationTime = 0;
                _removedAni.Add(effectAniInfo.Key);
            } 
        }

        foreach (var aniName in _removedAni)
        {
            RemoveEffectAni(aniName);
        }
    }

    protected void RemoveAllEffectAnis()
    {
        foreach (var effectAni in this._dictAnis.Values)
        {
            effectAni.Release();
        }
        _dictAnis.Clear();
    }
}