using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// IronSource SDK 接口封装
/// </summary>
public class IronsourceSdk : MonoBehaviour
{
    private static IronsourceSdk _instance;
    public static IronsourceSdk Instance
    {
        get
        {
            if (_instance == null)
            {
                GameObject go = new GameObject("IronsourceCallback");
                DontDestroyOnLoad(go);
                _instance = go.AddComponent<IronsourceSdk>();
            }
            return _instance;
        }
    }

    // 广告回调
    private Action _onRewardedVideoAdRewarded;
    private Action _onRewardedVideoAdClosed;
    private Action<int, string> _onRewardedVideoAdShowFailed;

    private bool _isInitialized = false;
    private bool _isRewardedVideoAvailable = false;
    private bool _isRewarded = false;

    /// <summary>
    /// 初始化SDK
    /// </summary>
    /// <param name="appKey">IronSource应用Key</param>
    public void Init(string appKey, string userId)
    {
        if (_isInitialized)
            return;

        // IronSource.Agent.setMetaData("is_test_suite", "enable"); 
        IronSourceEvents.onSdkInitializationCompletedEvent += SdkInitializationCompletedEvent;

        IronSource.Agent.setUserId(userId);
        
        // 设置IronSource SDK的应用Key
        IronSource.Agent.init(appKey);

        // 注册广告事件
        RegisterRewardedVideoEvents();

        // 设置网络状态跟踪
        IronSource.Agent.shouldTrackNetworkState(true);
        //IronSource.Agent.validateIntegration();

        _isInitialized = true;
        Debug.Log("IronsourceSdk initialized with app key: " + appKey);


    }

    private void SdkInitializationCompletedEvent(){
        //Launch test suite
        // IronSource.Agent.launchTestSuite();
    }

    /// <summary>
    /// 注册激励视频广告事件
    /// </summary>
    private void RegisterRewardedVideoEvents()
    {
        // 添加激励视频广告事件监听
        IronSourceRewardedVideoEvents.onAdOpenedEvent += RewardedVideoOnAdOpenedEvent;
        IronSourceRewardedVideoEvents.onAdClosedEvent += RewardedVideoOnAdClosedEvent;
        IronSourceRewardedVideoEvents.onAdAvailableEvent += RewardedVideoOnAdAvailable;
        IronSourceRewardedVideoEvents.onAdUnavailableEvent += RewardedVideoOnAdUnavailable;
        IronSourceRewardedVideoEvents.onAdLoadFailedEvent += RewardedVideoOnAdLoadFailedEvent;
        IronSourceRewardedVideoEvents.onAdShowFailedEvent += RewardedVideoOnAdShowFailedEvent;
        IronSourceRewardedVideoEvents.onAdRewardedEvent += RewardedVideoOnAdRewardedEvent;
        IronSourceRewardedVideoEvents.onAdClickedEvent += RewardedVideoOnAdClickedEvent;
    }

    /// <summary>
    /// 显示激励视频广告
    /// </summary>
    /// <param name="placementName">广告位名称，可选</param>
    /// <param name="onRewarded">奖励回调</param>
    /// <param name="onCancelled">取消回调</param>
    /// <param name="onFailed">失败回调</param>
    public void ShowRewardedVideo(string placementName = null, Action onRewarded = null, Action onCancelled = null, Action<int, string> onFailed = null)
    {
        // 保存回调
        _onRewardedVideoAdRewarded = onRewarded;
        _onRewardedVideoAdClosed = onCancelled;
        _onRewardedVideoAdShowFailed = onFailed;

        // 检查广告是否可用
        if (!IsRewardedVideoAvailable())
        {
            IronSource.Agent.loadRewardedVideo();
            Debug.LogWarning("IronsourceSdk: Rewarded video is not available");
            onFailed?.Invoke(-1, "Rewarded video is not available");
            return;
        }

        _isRewarded = false;
        // 显示广告
        if (string.IsNullOrEmpty(placementName))
        {
            IronSource.Agent.showRewardedVideo();
        }
        else
        {
            IronSource.Agent.showRewardedVideo(placementName);
        }
    }

    /// <summary>
    /// 检查激励视频广告是否可用
    /// </summary>
    /// <returns>是否可用</returns>
    public bool IsRewardedVideoAvailable()
    {
        return IronSource.Agent.isRewardedVideoAvailable();
    }

    #region 激励视频广告事件回调

    // 广告可用
    void RewardedVideoOnAdAvailable(IronSourceAdInfo adInfo)
    {
        Debug.Log("IronsourceSdk: Rewarded video ad available");
        _isRewardedVideoAvailable = true;
    }

    // 广告不可用
    void RewardedVideoOnAdUnavailable()
    {
        Debug.Log("IronsourceSdk: Rewarded video ad unavailable");
        _isRewardedVideoAvailable = false;
    }

    void RewardedVideoOnAdLoadFailedEvent(IronSourceError error)
    {
        Debug.LogError($"IronsourceSdk: Rewarded video ad load failed. Error code: {error.getCode()}, description: {error.getDescription()}");
    }

    // 广告打开
    void RewardedVideoOnAdOpenedEvent(IronSourceAdInfo adInfo)
    {
        Debug.Log("IronsourceSdk: Rewarded video ad opened");
    }

    // 广告关闭
    void RewardedVideoOnAdClosedEvent(IronSourceAdInfo adInfo)
    {
        Debug.Log("IronsourceSdk: Rewarded video ad closed");
        if (_isRewarded)
        {
            _onRewardedVideoAdRewarded?.Invoke();
        }
        _onRewardedVideoAdClosed?.Invoke();
    }

    // 广告奖励
    void RewardedVideoOnAdRewardedEvent(IronSourcePlacement placement, IronSourceAdInfo adInfo)
    {
        Debug.Log("IronsourceSdk: Rewarded video ad rewarded");
        _isRewarded = true;
    }

    // 广告显示失败
    void RewardedVideoOnAdShowFailedEvent(IronSourceError error, IronSourceAdInfo adInfo)
    {
        Debug.LogError($"IronsourceSdk: Rewarded video ad show failed. Error code: {error.getCode()}, description: {error.getDescription()}");
        _onRewardedVideoAdShowFailed?.Invoke(error.getCode(), error.getDescription());
    }

    // 广告点击
    void RewardedVideoOnAdClickedEvent(IronSourcePlacement placement, IronSourceAdInfo adInfo)
    {
        Debug.Log("IronsourceSdk: Rewarded video ad clicked");
    }

    #endregion

    /// <summary>
    /// 暂停SDK
    /// </summary>
    public void OnPause()
    {
        IronSource.Agent.onApplicationPause(true);
    }

    /// <summary>
    /// 恢复SDK
    /// </summary>
    public void OnResume()
    {
        IronSource.Agent.onApplicationPause(false);
    }

    /// <summary>
    /// Unity生命周期方法
    /// </summary>
    private void OnApplicationPause(bool isPaused)
    {
        IronSource.Agent.onApplicationPause(isPaused);
    }
}
